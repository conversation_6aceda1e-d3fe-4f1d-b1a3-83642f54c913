# confettiwish

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Compile and Minify for Production

```sh
npm run build
```

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```

## Push Notifications

ConfettiWish supports push notifications for birthday reminders and other important events:

- **Mobile**: Uses Capacitor Push Notifications plugin for native mobile notifications
- **Web**: Uses native Web Push API with VAPID keys for browser notifications
- **Cross-platform**: Unified notification system works across all platforms without Firebase dependencies

For setup instructions, see [Web Push Setup Guide](docs/web-push-setup.md).
