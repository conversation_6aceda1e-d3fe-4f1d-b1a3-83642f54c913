# Apple Push Notification Service (APNS) Setup Guide

This guide will help you set up Apple Push Notification Service for ConfettiWish to enable push notifications on iOS devices.

## Prerequisites

- Apple Developer account ($99/year)
- Xcode installed on a Mac
- Access to the Apple Developer portal

## Steps

### 1. Create an App ID

1. Go to the [Apple Developer Portal](https://developer.apple.com/account/)
2. Navigate to "Certificates, IDs & Profiles"
3. Select "Identifiers" from the sidebar
4. Click the "+" button to register a new App ID
5. Select "App IDs" and click "Continue"
6. Fill in the form:
   - Description: "ConfettiWish"
   - Bundle ID: "io.confettiwish.app"
7. Scroll down to "Capabilities" and enable "Push Notifications"
8. Click "Continue" and then "Register"

### 2. Create a Push Notification Key

1. In the Apple Developer Portal, go to "Certificates, IDs & Profiles"
2. Select "Keys" from the sidebar
3. Click the "+" button to create a new key
4. Enter a name: "ConfettiWish Push Key"
5. Check the "Apple Push Notifications service (APNs)" checkbox
6. Click "Continue" and then "Register"
7. Download the key file (.p8) - **Important**: You can only download this file once!
8. Note the Key ID shown on the page

### 3. Configure iOS App for Push Notifications

**Note**: ConfettiWish now uses native Capacitor Push Notifications without Firebase dependencies.

1. Ensure your iOS app is configured with the correct bundle ID: "io.confettiwish.app"
2. The APNs certificate/key will be used directly by your push notification service
3. No Firebase configuration is required for iOS push notifications

### 4. Configure Push Notification Service

1. Use the .p8 key file with your push notification service
2. Configure your server to send push notifications directly to APNs
3. Use the Key ID and Team ID in your server configuration
4. For ConfettiWish, this is handled by Supabase Edge Functions with Web Push API

### 5. Configure Capacitor

1. Open `confettiwish/ios/App/App/Info.plist`
2. Add the following entries if they don't exist:
   ```xml
   <key>UIBackgroundModes</key>
   <array>
     <string>remote-notification</string>
   </array>
   ```

### 6. Update Entitlements

1. Open Xcode and open the iOS project in `confettiwish/ios/App`
2. Select the "App" target
3. Go to the "Signing & Capabilities" tab
4. Click "+ Capability" and add "Push Notifications"
5. Also add "Background Modes" and check "Remote notifications"

## Testing APNS

To test that APNS is working correctly:

1. Build and run your iOS app on a physical device (not simulator)
2. Make sure the app requests notification permissions
3. Use the test notification feature in the app settings
4. Check Supabase Edge Function logs for delivery status

## Troubleshooting

- **Notifications not showing**: Check that the device has granted notification permissions
- **Token registration failing**: Verify the provisioning profile includes push capabilities
- **Certificate issues**: Ensure your Apple Developer account is active and the key is valid
- **Edge function errors**: Check Supabase logs for detailed error messages

## Resources

- [Apple Push Notification Service Documentation](https://developer.apple.com/documentation/usernotifications)
- [Capacitor Push Notifications Plugin](https://capacitorjs.com/docs/apis/push-notifications)
- [Web Push Setup Guide](web-push-setup.md)
