# Firebase Cloud Messaging Setup Guide

This guide will help you set up Firebase Cloud Messaging (FCM) for ConfettiWish to enable push notifications on Android devices.

## Prerequisites

- Google account
- Access to the Firebase console
- Android Studio (for testing)

## Steps

### 1. Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click "Add project"
3. Enter "ConfettiWish" as the project name
4. Follow the setup wizard (you can disable Google Analytics if not needed)
5. Click "Create project"

### 2. Register Your Android App

1. In the Firebase console, click the Android icon to add an Android app
2. Enter the package name: `io.confettiwish.app`
3. Enter a nickname (optional): "ConfettiWish Android"
4. Enter the SHA-1 signing certificate (optional for development)
5. Click "Register app"

### 3. Download Configuration File

1. Download the `google-services.json` file
2. Place it in the `confettiwish/android/app/` directory

### 4. Configure Supabase Edge Functions

1. Generate a Firebase Admin SDK private key:
   - Go to Project Settings > Service accounts
   - Click "Generate new private key"
   - Save the JSON file securely

2. Add the Firebase service account to Supabase:
   - Go to your Supabase project dashboard
   - Navigate to Settings > API
   - Under "Project Settings", find "Environment Variables"
   - Add a new secret:
     - Name: `FIREBASE_SERVICE_ACCOUNT`
     - Value: Paste the entire JSON content of your Firebase service account key

3. Deploy the Edge Functions:
   ```bash
   cd confettiwish
   supabase functions deploy send-notifications
   supabase functions deploy birthday-reminders
   ```

### 5. Set Up Scheduled Triggers (Optional)

For automatic birthday reminders, set up a scheduled trigger:

1. Go to your Supabase project dashboard
2. Navigate to Database > Functions
3. Create a new cron job:
   - Schedule: `0 8 * * *` (runs daily at 8 AM)
   - Function: `birthday-reminders`
   - HTTP Method: POST
   - Headers: `Authorization: Bearer {service_role_key}`

## Testing FCM

To test that FCM is working correctly:

1. Build and run your Android app on a device or emulator
2. Make sure the app requests notification permissions
3. Use the test notification feature in the app settings
4. Check the Firebase console under "Cloud Messaging" to see delivery statistics

## Troubleshooting

- **Notifications not showing**: Check that the device has granted notification permissions
- **Token registration failing**: Verify the `google-services.json` file is in the correct location
- **Edge function errors**: Check Supabase logs for detailed error messages

## Resources

- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Capacitor Push Notifications Plugin](https://capacitorjs.com/docs/apis/push-notifications)
- [Supabase Edge Functions](https://supabase.com/docs/guides/functions)
