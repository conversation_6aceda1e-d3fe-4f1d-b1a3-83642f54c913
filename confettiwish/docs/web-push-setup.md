# Web Push Notifications Setup Guide

This guide will help you set up Web Push notifications for ConfettiWish using native Web Push API and Supabase Edge Functions.

## Prerequisites

- Supabase project with Edge Functions enabled
- VAPID keys for Web Push
- SSL certificate (required for Web Push)

## Steps

### 1. Generate VAPID Keys

VAPID (Voluntary Application Server Identification) keys are required for Web Push notifications.

You can generate VAPID keys using the `web-push` npm package:

```bash
npm install -g web-push
web-push generate-vapid-keys
```

This will output:
```
=======================================
Public Key:
BEl62iUYgUivxIkv69yViEuiBIa40HI0DLLat3eAALZ-4kYNODAjmh_C_8k4_2sNgxAOhWKdNdGRdlz-BIKzaVg

Private Key:
tUxbFFHGAqHSfLjdKd6VgKX_VJSFfIlOtIllOcZ_5jM
=======================================
```

### 2. Configure Supabase Environment Variables

Add the following environment variables to your Supabase project:

1. Go to your Supabase project dashboard
2. Navigate to Settings > Edge Functions
3. Add the following secrets:

```
VAPID_PUBLIC_KEY=your-public-key-here
VAPID_PRIVATE_KEY=your-private-key-here
VAPID_SUBJECT=mailto:<EMAIL>
```

### 3. Configure Client Environment Variables

Add the VAPID public key to your client environment:

```env
VITE_VAPID_PUBLIC_KEY=your-public-key-here
```

### 4. Deploy Edge Functions

Deploy the updated Edge Functions:

```bash
cd confettiwish
supabase functions deploy send-notifications
supabase functions deploy get-notification-token
```

### 5. Test Web Push Notifications

To test that Web Push is working correctly:

1. Build and run your web application with HTTPS
2. Grant notification permissions when prompted
3. Use the test notification feature in the app settings
4. Check the browser's developer tools for any errors

## Architecture

### Web Push Flow

1. **Permission Request**: Browser requests notification permission from user
2. **Subscription Creation**: Browser creates a push subscription with the push service
3. **Registration**: Client sends subscription details to Supabase
4. **Notification Sending**: Server uses Web Push API to send notifications
5. **Display**: Service worker receives push event and displays notification

### Components

- **Service Worker** (`service-worker.js`): Handles push events and displays notifications
- **WebPushService** (`src/services/WebPushService.js`): Manages push subscriptions
- **NotificationService** (`src/services/NotificationService.js`): Handles permission requests
- **Edge Functions**: Send notifications using Web Push API

## Security Considerations

1. **VAPID Keys**: Keep private keys secure and never expose them in client code
2. **HTTPS Required**: Web Push only works over HTTPS
3. **User Consent**: Always request permission before subscribing to notifications
4. **Token Management**: Clean up expired/invalid push subscriptions

## Troubleshooting

### Notifications Not Showing

- Check that the browser has granted notification permissions
- Verify HTTPS is being used (required for Web Push)
- Check browser developer tools for service worker errors
- Ensure VAPID keys are correctly configured

### Edge Function Errors

- Check Supabase Edge Function logs for detailed error messages
- Verify environment variables are set correctly
- Test the Web Push endpoint manually

### Browser Compatibility

Web Push is supported in:
- Chrome 42+
- Firefox 44+
- Safari 16+ (macOS 13+, iOS 16.4+)
- Edge 17+

## Resources

- [Web Push Protocol](https://tools.ietf.org/html/rfc8030)
- [Push API Documentation](https://developer.mozilla.org/en-US/docs/Web/API/Push_API)
- [Service Worker API](https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API)
- [Supabase Edge Functions](https://supabase.com/docs/guides/functions)
