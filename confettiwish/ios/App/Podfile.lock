PODS:
  - Capacitor (7.2.0):
    - Capac<PERSON><PERSON><PERSON>ova
  - CapacitorCommunityContacts (7.0.0):
    - Capacitor
  - Capacitor<PERSON>ordova (7.2.0)
  - CapacitorPushNotifications (7.0.1):
    - Capacitor

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorCommunityContacts (from `../../node_modules/@capacitor-community/contacts`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorPushNotifications (from `../../node_modules/@capacitor/push-notifications`)"

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorCommunityContacts:
    :path: "../../node_modules/@capacitor-community/contacts"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorPushNotifications:
    :path: "../../node_modules/@capacitor/push-notifications"

SPEC CHECKSUMS:
  Capacitor: 03bc7cbdde6a629a8b910a9d7d78c3cc7ed09ea7
  CapacitorCommunityContacts: aaa9f95f2da72b5de15d5fa935d736d07c644db2
  CapacitorCordova: 5967b9ba03915ef1d585469d6e31f31dc49be96f
  CapacitorPushNotifications: 6a2794788c583dd89215f1805ca4bced1b13dbdf

PODFILE CHECKSUM: c88be6644f997ff30ec9b04e30d82e204f04c623

COCOAPODS: 1.16.2
