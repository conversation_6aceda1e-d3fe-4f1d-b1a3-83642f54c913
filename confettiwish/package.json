{"name": "confettiwish", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "format": "prettier --write src/", "cap:sync": "cap sync", "cap:build": "vite build && cap sync", "cap:ios": "cap open ios", "cap:android": "cap open android", "cap:serve": "vite build && cap sync && cap run android"}, "dependencies": {"@capacitor-community/contacts": "^7.0.0", "@capacitor/android": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/ios": "^7.2.0", "@capacitor/push-notifications": "^7.0.0", "@mdi/font": "^7.4.47", "@supabase/supabase-js": "^2.39.0", "papaparse": "^5.5.2", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuetify": "^3.8.2"}, "devDependencies": {"@capacitor/cli": "^7.2.0", "@eslint/js": "^9.22.0", "@vite-pwa/assets-generator": "^1.0.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "globals": "^16.0.0", "prettier": "3.5.3", "vite": "^6.2.4", "vite-plugin-pwa": "^1.0.0", "vite-plugin-vue-devtools": "^7.7.2", "workbox-build": "^7.3.0", "workbox-core": "^7.3.0", "workbox-precaching": "^7.3.0", "workbox-routing": "^7.3.0", "workbox-strategies": "^7.3.0", "workbox-window": "^7.3.0"}}