import { initializeApp } from 'firebase/app';
import { getMessaging } from 'firebase/messaging/sw';

// Initialize Firebase
const firebaseConfig = {
  apiKey: self.__env.FIREBASE_API_KEY,
  authDomain: self.__env.FIREBASE_AUTH_DOMAIN,
  projectId: self.__env.FIREBASE_PROJECT_ID,
  storageBucket: self.__env.FIREBASE_STORAGE_BUCKET,
  messagingSenderId: self.__env.FIREBASE_MESSAGING_SENDER_ID,
  appId: self.__env.FIREBASE_APP_ID
};

const app = initializeApp(firebaseConfig);
const messaging = getMessaging(app);

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);
  
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: '/icon.png'
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});
