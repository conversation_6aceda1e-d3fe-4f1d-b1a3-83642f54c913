// ConfettiWish Service Worker for Push Notifications
// Using native Web Push API without Firebase

// Handle push events from the server
self.addEventListener('push', function(event) {
  console.log('[service-worker.js] Push event received:', event);

  if (!event.data) {
    console.log('Push event but no data');
    return;
  }

  try {
    const payload = event.data.json();
    console.log('[service-worker.js] Push payload:', payload);

    const notificationTitle = payload.title || 'ConfettiWish';
    const notificationOptions = {
      body: payload.body || 'You have a new notification',
      icon: '/icons/icon-192x192.png',
      badge: '/icons/icon-72x72.png',
      data: payload.data || {},
      tag: payload.tag || 'confettiwish-notification',
      requireInteraction: false,
      actions: payload.actions || []
    };

    event.waitUntil(
      self.registration.showNotification(notificationTitle, notificationOptions)
    );
  } catch (error) {
    console.error('[service-worker.js] Error parsing push payload:', error);

    // Fallback notification
    event.waitUntil(
      self.registration.showNotification('ConfettiWish', {
        body: 'You have a new notification',
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png'
      })
    );
  }
});

// Handle notification click events
self.addEventListener('notificationclick', function(event) {
  console.log('[service-worker.js] Notification click received:', event);

  event.notification.close();

  // Get the URL to open from the notification data
  const urlToOpen = event.notification.data?.url || '/';

  event.waitUntil(
    self.clients.matchAll({ type: 'window', includeUncontrolled: true }).then(function(clientList) {
      // Check if there's already a window/tab open with the target URL
      for (let i = 0; i < clientList.length; i++) {
        const client = clientList[i];
        if (client.url === urlToOpen && 'focus' in client) {
          return client.focus();
        }
      }

      // If no existing window/tab, open a new one
      if (self.clients.openWindow) {
        return self.clients.openWindow(urlToOpen);
      }
    })
  );
});

// Handle notification close events
self.addEventListener('notificationclose', function(event) {
  console.log('[service-worker.js] Notification closed:', event);
  // You can track notification dismissals here if needed
});
