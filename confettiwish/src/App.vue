<script setup>
import { RouterView, useRoute } from 'vue-router'
import { computed } from 'vue'
import { useTheme } from '@/composables/useTheme'
import { useAuth } from '@/composables/useAuth'
import OnboardingSplash from '@/components/OnboardingSplash.vue'
import BottomNavigation from '@/components/BottomNavigation.vue'
import NotificationPermission from '@/components/NotificationPermission.vue'

const route = useRoute()
const themeStore = useTheme()
const authStore = useAuth()

// Only show the splash on login and onboarding screens
const showSplash = computed(() => {
  return route.name === 'login' ||
         route.name === 'personalInfo' ||
         route.name === 'importContacts' ||
         route.name === 'selectContacts'
})

// Only show the bottom navigation on main app screens
const showBottomNav = computed(() => {
  return route.name === 'birthdays' ||
         route.name === 'friends' ||
         route.name === 'profile' ||
         route.name === 'home'
})
</script>

<template>
  <v-app :theme="themeStore.isDark ? 'dark' : 'light'">
    <v-main>
      <v-container class="pa-0" fluid>
        <header class="header-section">
          <v-container class="py-0">
            <v-row justify="center" align="center" class="my-0">
              <v-col cols="12" class="text-center py-2">
                <img alt="ConfettiWish logo" class="logo" src="@/assets/confettiwish.png" width="200" height="133" />
                <OnboardingSplash v-if="showSplash" />
              </v-col>
            </v-row>
          </v-container>
        </header>

        <v-container class="router-view-container pt-0">
          <RouterView />
        </v-container>
      </v-container>
    </v-main>

    <!-- Bottom Navigation -->
    <BottomNavigation v-if="showBottomNav" />

    <!-- Notification Permission Request -->
    <NotificationPermission v-if="authStore.isAuthenticated" />
  </v-app>
</template>

<style scoped>
/* Header styles moved to main.css for global access */

.logo {
  display: block;
  margin: 0 auto 0.5rem;
}

.router-view-container {
  padding-bottom: 60px; /* Add padding for bottom navigation */
}
</style>
