<script setup>
import { computed } from 'vue'
import { useNotifications } from '@/composables/useNotifications'
import { useRouter } from 'vue-router'
import { formatDistanceToNow } from '@/utils/dateUtils'

const props = defineProps({
  maxItems: {
    type: Number,
    default: 5
  }
})

const notificationsStore = useNotifications()
const router = useRouter()

// Get limited list of notifications
const notificationsList = computed(() => {
  return notificationsStore.notifications.slice(0, props.maxItems)
})

// Check if there are any notifications
const hasNotifications = computed(() => {
  return notificationsList.value.length > 0
})

// Handle notification click
function handleNotificationClick(notification) {
  // Mark as read
  notificationsStore.markAsRead(notification.id)
  
  // Navigate to the appropriate page if URL is provided
  if (notification.data && notification.data.url) {
    router.push(notification.data.url)
  }
}

// Mark all as read
function markAllAsRead() {
  const { data: { user } } = supabase.auth.getUser()
  if (user) {
    notificationsStore.markAllAsRead(user.id)
  }
}
</script>

<template>
  <div class="notification-list">
    <div v-if="hasNotifications" class="notification-header">
      <div class="notification-title">Notifications</div>
      <v-btn
        variant="text"
        density="comfortable"
        size="small"
        @click="markAllAsRead"
      >
        Mark all as read
      </v-btn>
    </div>
    
    <v-list v-if="hasNotifications" lines="two">
      <v-list-item
        v-for="notification in notificationsList"
        :key="notification.id"
        :title="notification.title"
        :subtitle="notification.body"
        :class="{ 'unread': !notification.read }"
        @click="handleNotificationClick(notification)"
      >
        <template v-slot:prepend>
          <v-avatar color="primary" size="36">
            <v-icon icon="mdi-bell"></v-icon>
          </v-avatar>
        </template>
        
        <template v-slot:append>
          <div class="notification-time">
            {{ formatDistanceToNow(new Date(notification.created_at)) }}
          </div>
        </template>
      </v-list-item>
    </v-list>
    
    <div v-else class="no-notifications">
      <v-icon icon="mdi-bell-off" size="large" color="grey"></v-icon>
      <div class="no-notifications-text">No notifications</div>
    </div>
  </div>
</template>

<style scoped>
.notification-list {
  width: 100%;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.notification-title {
  font-weight: bold;
  font-size: 1.1rem;
}

.unread {
  background-color: rgba(var(--v-theme-primary), 0.1);
}

.notification-time {
  font-size: 0.8rem;
  color: rgba(0, 0, 0, 0.6);
}

.no-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
}

.no-notifications-text {
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.6);
}
</style>
