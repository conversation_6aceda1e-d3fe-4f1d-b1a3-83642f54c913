<script setup>
import { ref, onMounted, computed } from 'vue'
import { useNotifications } from '@/composables/useNotifications'
import { useAuth } from '@/composables/useAuth'
import { NotificationService } from '@/services/NotificationService'
import { WebPushService } from '@/services/WebPushService'

const notificationsStore = useNotifications()
const authStore = useAuth()
const showPrompt = ref(false)
const permissionStatus = ref('default')
const showDeniedAlert = computed(() => {
  return NotificationService.isWebNotificationAvailable() &&
         permissionStatus.value === 'denied' &&
         notificationsStore.isEnabled
})

// Check if we should show the notification permission prompt
onMounted(async () => {
  // Only show if user is logged in
  if (!authStore.user) return

  // Check current permission status
  if (NotificationService.isWebNotificationAvailable()) {
    permissionStatus.value = Notification.permission
    console.log('Current notification permission status:', permissionStatus.value)
  }

  // Check if notifications are already enabled
  if (notificationsStore.isEnabled) return

  // Check if we're on mobile with push notifications
  if (NotificationService.isPushAvailable()) {
    showPrompt.value = true
    return
  }

  // Check if web notifications are available but not granted
  if (NotificationService.isWebNotificationAvailable() && Notification.permission === 'default') {
    showPrompt.value = true
  }
})

// Enable notifications
async function enableNotifications() {
  if (authStore.user) {
    console.log('Enabling notifications...')
    const userId = authStore.user.id

    // Request notification permission first for web
    if (NotificationService.isWebNotificationAvailable()) {
      try {
        const permission = await Notification.requestPermission()
        permissionStatus.value = permission
        console.log('Updated permission status:', permissionStatus.value)

        // If permission was denied, don't proceed with enabling notifications
        if (permissionStatus.value === 'denied') {
          console.log('Notification permission denied by user')
          showPrompt.value = false
          return
        }

        // Register for web push notifications if permission is granted
        if (permissionStatus.value === 'granted') {
          await NotificationService.initializeWebNotifications(userId)
        }
      } catch (error) {
        console.error('Error requesting notification permission:', error)
        permissionStatus.value = 'denied'
        showPrompt.value = false
        return
      }
    }

    // Now toggle notifications in the store
    const result = await notificationsStore.toggleNotifications(true, userId)
    console.log('Toggle notifications result:', result)

    showPrompt.value = false
  }
}

// Dismiss the prompt
function dismissPrompt() {
  showPrompt.value = false
}

// Open browser settings to help user enable notifications
function openSettings() {
  // This is a workaround to help users find browser notification settings
  alert('To enable notifications, please:\n\n' +
        '1. Click on the lock/info icon in your browser address bar\n' +
        '2. Find "Notifications" in the site settings\n' +
        '3. Change the setting to "Allow"\n\n' +
        'After enabling notifications, please refresh this page.')
}
</script>

<template>
  <div>
    <!-- Permission prompt -->
    <v-snackbar
      v-model="showPrompt"
      :timeout="-1"
      color="primary"
      location="top"
    >
      <div class="d-flex align-center">
        <v-icon icon="mdi-bell" class="mr-2"></v-icon>
        <span>Enable notifications to stay updated on birthdays and wishes</span>
      </div>

      <template v-slot:actions>
        <v-btn
          variant="text"
          @click="enableNotifications"
        >
          Enable
        </v-btn>
        <v-btn
          variant="text"
          @click="dismissPrompt"
        >
          Not now
        </v-btn>
      </template>
    </v-snackbar>

    <!-- Alert for denied permissions -->
    <v-alert
      v-if="showDeniedAlert"
      type="warning"
      variant="tonal"
      class="mb-4"
    >
      <div class="d-flex flex-column">
        <div class="text-subtitle-1 mb-2">
          Notifications are blocked
        </div>
        <div class="mb-3">
          You've enabled notifications in the app, but they're blocked in your browser.
          To receive birthday reminders and other updates, please enable notifications in your browser settings.
        </div>
        <v-btn
          color="warning"
          @click="openSettings"
          class="align-self-start"
        >
          How to Enable Notifications
        </v-btn>
      </div>
    </v-alert>
  </div>
</template>

<style scoped>
.notification-permission {
  margin-bottom: 16px;
}
</style>
