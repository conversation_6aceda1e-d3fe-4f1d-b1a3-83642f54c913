import { inject } from 'vue'
import { useNotificationsStore } from '@/stores/notifications'
import { storeToRefs } from 'pinia'
import { computed } from 'vue'
import { supabase } from '@/lib/supabase'

/**
 * Composable for working with notifications
 * @returns {Object} Notification methods and properties
 */
export function useNotifications() {
  const pinia = inject('pinia')
  if (!pinia) {
    throw new Error('Pinia not properly initialized')
  }

  const store = useNotificationsStore(pinia)

  // Extract refs from the store to maintain reactivity
  const {
    notifications,
    isEnabled,
    loading,
    error,
    deviceToken,
    unreadCount
  } = storeToRefs(store)

  /**
   * Get notification preferences
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} Notification preferences
   */
  async function getNotificationPreferences(userId) {
    if (!userId) return null

    try {
      const { data, error } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error && error.code !== 'PGRST116') { // Not found error
        console.error('Error loading notification preferences:', error)
        return null
      }

      if (data) {
        return {
          enabled: data.enabled,
          birthdayReminders: data.birthday_reminders,
          birthdayReminderDays: data.birthday_reminder_days || [1, 7],
          wishUpdates: data.wish_updates,
          friendActivity: data.friend_activity
        }
      }

      // Return default preferences if none found
      return {
        enabled: isEnabled.value,
        birthdayReminders: true,
        birthdayReminderDays: [1, 7],
        wishUpdates: true,
        friendActivity: true
      }
    } catch (error) {
      console.error('Error getting notification preferences:', error)
      return null
    }
  }

  /**
   * Update notification preferences
   * @param {string} userId - The user ID
   * @param {Object} preferences - Notification preferences
   * @returns {Promise<boolean>} Success status
   */
  async function updateNotificationPreferences(userId, preferences) {
    if (!userId) return false

    try {
      // Update in Supabase
      const { error } = await supabase
        .from('notification_preferences')
        .upsert({
          user_id: userId,
          enabled: preferences.enabled,
          birthday_reminders: preferences.birthdayReminders,
          birthday_reminder_days: preferences.birthdayReminderDays,
          wish_updates: preferences.wishUpdates,
          friend_activity: preferences.friendActivity
        }, { onConflict: 'user_id' })

      if (error) {
        console.error('Error saving notification preferences:', error)
        return false
      }

      // Update local state
      if (isEnabled.value !== preferences.enabled) {
        await store.toggleNotifications(preferences.enabled, userId)
      }

      return true
    } catch (error) {
      console.error('Error updating notification preferences:', error)
      return false
    }
  }

  /**
   * Send a notification through the backend
   * @param {string} userId - The user ID
   * @param {string} type - Notification type
   * @param {Object} data - Notification data
   * @returns {Promise<Object>} Response from the server
   */
  async function sendNotification(userId, type, data) {
    if (!userId) return { success: false, error: 'User ID is required' }

    try {
      const { data: response, error } = await supabase.functions.invoke('send-notifications', {
        body: {
          type,
          userId,
          data
        }
      })

      if (error) {
        console.error('Error sending notification:', error)
        return { success: false, error: error.message }
      }

      return { success: true, data: response }
    } catch (error) {
      console.error('Error sending notification:', error)
      return { success: false, error: error.message }
    }
  }

  return {
    ...store,
    getNotificationPreferences,
    updateNotificationPreferences,
    sendNotification
  }
}
