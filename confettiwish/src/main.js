import './assets/main.css'
import 'vuetify/styles'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import { authPlugin } from './plugins/auth'
import { registerSW } from 'virtual:pwa-register'

// Vuetify
import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'

// Import MDI CSS directly from node_modules
import '/node_modules/@mdi/font/css/materialdesignicons.css'

const vuetify = createVuetify({
    components,
    directives,
    icons: {
      defaultSet: 'mdi',
    },
})

const app = createApp(App)
const pinia = createPinia()

app.provide('pinia', pinia)
app.use(pinia)
app.use(vuetify)
app.use(router)
app.use(authPlugin, router)

app.mount('#app')

// Register service worker for PWA
const updateSW = registerSW({
  onNeedRefresh() {
    // Show a prompt to the user to refresh for new content
    if (confirm('New content available. Reload?')) {
      updateSW()
    }
  },
  onOfflineReady() {
    console.log('App ready to work offline')
  }
})
