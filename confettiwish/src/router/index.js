import { createRouter, createWebHistory } from 'vue-router'
import LoginView from '../views/LoginView.vue'
import PersonalInfoView from '../views/PersonalInfoView.vue'
import ImportContactsView from '../views/ImportContactsView.vue'
import SelectContactsView from '../views/SelectContactsView.vue'
import BirthdayTimelineView from '../views/BirthdayTimelineView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // Auth routes
    {
      path: '/auth/login',
      name: 'login',
      component: LoginView,
      meta: { requiresGuest: true }
    },
    {
      path: '/auth/personal-info',
      name: 'personalInfo',
      component: PersonalInfoView,
      meta: { requiresAuth: true }
    },
    {
      path: '/auth/import-contacts',
      name: 'importContacts',
      component: ImportContactsView,
      meta: { requiresAuth: true }
    },
    {
      path: '/auth/select-contacts',
      name: 'selectContacts',
      component: SelectContactsView,
      meta: { requiresAuth: true }
    },

    // Main application routes
    {
      path: '/birthdays',
      name: 'birthdays',
      component: BirthdayTimelineView,
      meta: { requiresAuth: true, requiresOnboarding: true }
    },
    {
      path: '/friends',
      name: 'friends',
      component: () => import('../views/FriendsView.vue'),
      meta: { requiresAuth: true, requiresOnboarding: true }
    },
    {
      path: '/friends/add',
      name: 'addFriend',
      component: () => import('../views/AddFriendView.vue'),
      meta: { requiresAuth: true, requiresOnboarding: true }
    },
    {
      path: '/friends/:friendId/edit',
      name: 'editFriend',
      component: () => import('../views/EditFriendView.vue'),
      meta: { requiresAuth: true, requiresOnboarding: true }
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
      meta: { requiresAuth: true, requiresOnboarding: true }
    },
    {
      path: '/profile/edit',
      name: 'editProfile',
      component: () => import('../views/EditProfileView.vue'),
      meta: { requiresAuth: true, requiresOnboarding: true }
    },
    {
      path: '/friends/:friendId/wishes',
      name: 'wishes',
      component: () => import('../views/WishesView.vue'),
      meta: { requiresAuth: true, requiresOnboarding: true }
    },
    {
      path: '/friends/:friendId/wishes/add',
      name: 'addWish',
      component: () => import('../views/AddWishView.vue'),
      meta: { requiresAuth: true, requiresOnboarding: true }
    },
    {
      path: '/friends/:friendId/wishes/:wishId/edit',
      name: 'editWish',
      component: () => import('../views/EditWishView.vue'),
      meta: { requiresAuth: true, requiresOnboarding: true }
    },
    {
      path: '/settings/notifications',
      name: 'notificationSettings',
      component: () => import('../views/NotificationSettingsView.vue'),
      meta: { requiresAuth: true, requiresOnboarding: true }
    },

    // Default route - redirects to birthdays
    {
      path: '/',
      name: 'home',
      redirect: { name: 'birthdays' },
      meta: { requiresAuth: true, requiresOnboarding: true }
    }
  ]
})

export default router
