import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@/lib/supabase'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const profile = ref(null)
  const loading = ref(false)
  const error = ref(null)

  const isAuthenticated = computed(() => !!user.value)
  const isOnboardingComplete = computed(() => !!profile.value?.onboarding_complete)

  async function fetchProfile() {
    if (!user.value) return null

    try {
      const { data, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.value.id)
        .single()

      if (profileError) {
        // If the error is 'not found', create a new profile
        if (profileError.code === 'PGRST116') {
          const { data: newProfile, error: createError } = await supabase
            .from('profiles')
            .insert([
              {
                id: user.value.id,
                email: user.value.email,
                onboarding_complete: false
              }
            ])
            .select()
            .single()

          if (createError) {
            console.error('Error creating profile:', createError)
            return null
          }

          profile.value = newProfile
          return newProfile
        }

        console.error('Error fetching profile:', profileError)
        return null
      }

      profile.value = data
      return data
    } catch (e) {
      console.error('Unexpected error in fetchProfile:', e)
      return null
    }
  }

  async function sendOTP(email) {
    try {
      loading.value = true
      error.value = null

      const { error: otpError } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: true
        }
      })

      if (otpError) throw otpError

      return { success: true }
    } catch (e) {
      error.value = e.message
      return { success: false, error: e.message }
    } finally {
      loading.value = false
    }
  }

  async function verifyOTP(email, token) {
    try {
      loading.value = true
      error.value = null

      const { data, error: verifyError } = await supabase.auth.verifyOtp({
        email,
        token,
        type: 'email'
      })

      if (verifyError) throw verifyError

      user.value = data.user
      const userProfile = await fetchProfile()

      return {
        success: true,
        requiresOnboarding: !userProfile?.onboarding_complete
      }
    } catch (e) {
      error.value = e.message
      return { success: false, error: e.message }
    } finally {
      loading.value = false
    }
  }

  async function checkUser() {
    try {
      const { data: { user: sessionUser } } = await supabase.auth.getUser()
      user.value = sessionUser
      if (sessionUser) {
        await fetchProfile()
      }
      return { user: sessionUser, profile: profile.value }
    } catch (e) {
      user.value = null
      profile.value = null
      return { user: null, profile: null }
    }
  }

  async function signOut() {
    try {
      await supabase.auth.signOut()
      user.value = null
      profile.value = null
    } catch (e) {
      error.value = e.message
    }
  }

  async function updateProfile(profileData) {
    try {
      loading.value = true
      error.value = null

      if (!user.value) {
        throw new Error('User not authenticated')
      }

      const { data, error: updateError } = await supabase
        .from('profiles')
        .update(profileData)
        .eq('id', user.value.id)
        .select()

      if (updateError) throw updateError

      if (data && data.length > 0) {
        profile.value = data[0]
      }

      return { success: true, profile: profile.value }
    } catch (e) {
      error.value = e.message
      return { success: false, error: e.message }
    } finally {
      loading.value = false
    }
  }

  return {
    user,
    profile,
    isAuthenticated,
    isOnboardingComplete,
    loading,
    error,
    fetchProfile,
    sendOTP,
    verifyOTP,
    checkUser,
    signOut,
    updateProfile
  }
})
