import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@/lib/supabase'

export const useLocalizationStore = defineStore('localization', () => {
  // Default locale is based on browser settings, fallback to en-US
  const defaultLocale = navigator.language || 'en-US'
  const locale = ref(defaultLocale)
  const defaultCurrency = ref(null)
  const loading = ref(false)
  const error = ref(null)
  const isLoaded = ref(false)

  // Available currencies with symbols and names
  const availableCurrencies = [
    // Major global currencies
    { code: 'USD', symbol: '$', name: 'US Dollar' },
    { code: 'EUR', symbol: '€', name: 'Euro' },
    { code: 'GBP', symbol: '£', name: 'British Pound' },
    { code: 'JPY', symbol: '¥', name: 'Japanese Yen' },
    { code: 'CNY', symbol: '¥', name: 'Chinese Yuan' },
    { code: 'INR', symbol: '₹', name: 'Indian Rupee' },
    { code: 'RUB', symbol: '₽', name: 'Russian Ruble' },
    { code: 'BRL', symbol: 'R$', name: 'Brazilian Real' },
    { code: 'CAD', symbol: 'CA$', name: 'Canadian Dollar' },
    { code: 'AUD', symbol: 'A$', name: 'Australian Dollar' },

    // Other major currencies
    { code: 'CHF', symbol: 'CHF', name: 'Swiss Franc' },
    { code: 'HKD', symbol: 'HK$', name: 'Hong Kong Dollar' },
    { code: 'SGD', symbol: 'S$', name: 'Singapore Dollar' },
    { code: 'SEK', symbol: 'kr', name: 'Swedish Krona' },
    { code: 'NZD', symbol: 'NZ$', name: 'New Zealand Dollar' },
    { code: 'MXN', symbol: 'MX$', name: 'Mexican Peso' },
    { code: 'ZAR', symbol: 'R', name: 'South African Rand' },
    { code: 'TRY', symbol: '₺', name: 'Turkish Lira' },
    { code: 'NOK', symbol: 'kr', name: 'Norwegian Krone' },
    { code: 'DKK', symbol: 'kr', name: 'Danish Krone' },

    // Asian currencies
    { code: 'KRW', symbol: '₩', name: 'South Korean Won' },
    { code: 'IDR', symbol: 'Rp', name: 'Indonesian Rupiah' },
    { code: 'TWD', symbol: 'NT$', name: 'New Taiwan Dollar' },
    { code: 'THB', symbol: '฿', name: 'Thai Baht' },
    { code: 'PHP', symbol: '₱', name: 'Philippine Peso' },
    { code: 'MYR', symbol: 'RM', name: 'Malaysian Ringgit' },
    { code: 'VND', symbol: '₫', name: 'Vietnamese Dong' },

    // Middle Eastern currencies
    { code: 'AED', symbol: 'د.إ', name: 'UAE Dirham' },
    { code: 'SAR', symbol: '﷼', name: 'Saudi Riyal' },
    { code: 'ILS', symbol: '₪', name: 'Israeli New Shekel' },
    { code: 'QAR', symbol: '﷼', name: 'Qatari Rial' },

    // Latin American currencies
    { code: 'ARS', symbol: '$', name: 'Argentine Peso' },
    { code: 'CLP', symbol: '$', name: 'Chilean Peso' },
    { code: 'COP', symbol: '$', name: 'Colombian Peso' },
    { code: 'PEN', symbol: 'S/', name: 'Peruvian Sol' },

    // European currencies (non-Euro)
    { code: 'PLN', symbol: 'zł', name: 'Polish Złoty' },
    { code: 'CZK', symbol: 'Kč', name: 'Czech Koruna' },
    { code: 'HUF', symbol: 'Ft', name: 'Hungarian Forint' },
    { code: 'RON', symbol: 'lei', name: 'Romanian Leu' },

    // Other regional currencies
    { code: 'NGA', symbol: '₦', name: 'Nigerian Naira' },
    { code: 'EGP', symbol: 'E£', name: 'Egyptian Pound' },
    { code: 'PKR', symbol: '₨', name: 'Pakistani Rupee' },
    { code: 'BDT', symbol: '৳', name: 'Bangladeshi Taka' },
    { code: 'VEF', symbol: 'Bs.', name: 'Venezuelan Bolívar' },
    { code: 'UAH', symbol: '₴', name: 'Ukrainian Hryvnia' }
  ]

  // Currency symbols lookup object for quick access
  const currencySymbols = availableCurrencies.reduce((acc, curr) => {
    acc[curr.code] = curr.symbol
    return acc
  }, {})

  // Map of common locales to their default currency codes
  const localeToCurrency = {
    // English locales
    'en-US': 'USD',
    'en-GB': 'GBP',
    'en-CA': 'CAD',
    'en-AU': 'AUD',
    'en-NZ': 'NZD',
    'en-IE': 'EUR',
    'en-ZA': 'ZAR',
    'en-IN': 'INR',
    'en-SG': 'SGD',
    'en-HK': 'HKD',
    'en-PH': 'PHP',

    // European locales
    'fr-FR': 'EUR',
    'fr-CA': 'CAD',
    'fr-CH': 'CHF',
    'fr-BE': 'EUR',
    'de-DE': 'EUR',
    'de-AT': 'EUR',
    'de-CH': 'CHF',
    'it-IT': 'EUR',
    'it-CH': 'CHF',
    'es-ES': 'EUR',
    'es-MX': 'MXN',
    'es-AR': 'ARS',
    'es-CL': 'CLP',
    'es-CO': 'COP',
    'es-PE': 'PEN',
    'pt-PT': 'EUR',
    'pt-BR': 'BRL',
    'nl-NL': 'EUR',
    'nl-BE': 'EUR',
    'sv-SE': 'SEK',
    'da-DK': 'DKK',
    'no-NO': 'NOK',
    'fi-FI': 'EUR',
    'pl-PL': 'PLN',
    'cs-CZ': 'CZK',
    'hu-HU': 'HUF',
    'ro-RO': 'RON',
    'el-GR': 'EUR',
    'tr-TR': 'TRY',

    // Asian locales
    'ja-JP': 'JPY',
    'zh-CN': 'CNY',
    'zh-TW': 'TWD',
    'zh-HK': 'HKD',
    'zh-SG': 'SGD',
    'ko-KR': 'KRW',
    'th-TH': 'THB',
    'vi-VN': 'VND',
    'id-ID': 'IDR',
    'ms-MY': 'MYR',
    'hi-IN': 'INR',
    'bn-BD': 'BDT',
    'ur-PK': 'PKR',

    // Middle Eastern locales
    'ar-SA': 'SAR',
    'ar-AE': 'AED',
    'ar-EG': 'EGP',
    'ar-QA': 'QAR',
    'he-IL': 'ILS',

    // Other locales
    'ru-RU': 'RUB',
    'uk-UA': 'UAH'
  }

  // Helper function to normalize locale format (handles both hyphen and underscore formats)
  function normalizeLocale(localeString) {
    // If the locale uses underscore, convert it to hyphen format for consistency
    return localeString.replace('_', '-');
  }

  // Get currency code from locale or user preference
  const currencyCode = computed(() => {
    // If user has set a default currency, use that
    if (defaultCurrency.value) {
      return defaultCurrency.value
    }

    // Normalize the locale to hyphen format
    const normalizedLocale = normalizeLocale(locale.value)

    // Try to get currency from locale map
    if (localeToCurrency[normalizedLocale]) {
      return localeToCurrency[normalizedLocale]
    }

    // For other locales, try to guess based on region code
    const regionCode = normalizedLocale.split('-')[1]
    if (regionCode) {
      // Region to currency mappings
      // North America
      if (regionCode === 'US') return 'USD'
      if (regionCode === 'CA') return 'CAD'
      if (regionCode === 'MX') return 'MXN'

      // Europe
      if (regionCode === 'GB') return 'GBP'
      if (['FR', 'DE', 'IT', 'ES', 'PT', 'NL', 'BE', 'AT', 'FI', 'IE', 'GR', 'SK', 'SI', 'LV', 'LT', 'EE', 'MT', 'CY', 'LU'].includes(regionCode)) {
        return 'EUR' // Euro zone countries
      }
      if (regionCode === 'CH') return 'CHF'
      if (regionCode === 'SE') return 'SEK'
      if (regionCode === 'DK') return 'DKK'
      if (regionCode === 'NO') return 'NOK'
      if (regionCode === 'PL') return 'PLN'
      if (regionCode === 'CZ') return 'CZK'
      if (regionCode === 'HU') return 'HUF'
      if (regionCode === 'RO') return 'RON'
      if (regionCode === 'TR') return 'TRY'
      if (regionCode === 'UA') return 'UAH'

      // Asia Pacific
      if (regionCode === 'AU') return 'AUD'
      if (regionCode === 'NZ') return 'NZD'
      if (regionCode === 'JP') return 'JPY'
      if (regionCode === 'CN') return 'CNY'
      if (regionCode === 'HK') return 'HKD'
      if (regionCode === 'SG') return 'SGD'
      if (regionCode === 'KR') return 'KRW'
      if (regionCode === 'TW') return 'TWD'
      if (regionCode === 'TH') return 'THB'
      if (regionCode === 'VN') return 'VND'
      if (regionCode === 'ID') return 'IDR'
      if (regionCode === 'MY') return 'MYR'
      if (regionCode === 'PH') return 'PHP'
      if (regionCode === 'IN') return 'INR'
      if (regionCode === 'PK') return 'PKR'
      if (regionCode === 'BD') return 'BDT'

      // Middle East
      if (regionCode === 'SA') return 'SAR'
      if (regionCode === 'AE') return 'AED'
      if (regionCode === 'IL') return 'ILS'
      if (regionCode === 'QA') return 'QAR'
      if (regionCode === 'EG') return 'EGP'

      // Latin America
      if (regionCode === 'BR') return 'BRL'
      if (regionCode === 'AR') return 'ARS'
      if (regionCode === 'CL') return 'CLP'
      if (regionCode === 'CO') return 'COP'
      if (regionCode === 'PE') return 'PEN'

      // Africa
      if (regionCode === 'ZA') return 'ZAR'
      if (regionCode === 'NG') return 'NGA'

      // Russia and former Soviet states
      if (regionCode === 'RU') return 'RUB'
    }

    // Default to USD if we can't determine the currency
    return 'USD'
  })

  // Get currency symbol based on currency code
  const currencySymbol = computed(() => {
    // If we have a default currency, use its symbol
    if (defaultCurrency.value && currencySymbols[defaultCurrency.value]) {
      return currencySymbols[defaultCurrency.value]
    }

    try {
      // Try to get the currency symbol from the formatter
      const formatter = new Intl.NumberFormat(locale.value, {
        style: 'currency',
        currency: 'USD'
      })

      // Get the currency symbol from the formatted string
      const parts = formatter.formatToParts(0)
      const currencyPart = parts.find(part => part.type === 'currency')

      if (currencyPart) {
        return currencyPart.value
      }

      // Fallback to common currency symbols
      const regionCode = locale.value.split('-')[1]
      if (regionCode && currencySymbols[regionCode]) {
        return currencySymbols[regionCode]
      }

      // Default to $ if no match found
      return '$'
    } catch (e) {
      console.error('Error getting currency symbol:', e)
      return '$'
    }
  })

  // Format a number as currency
  function formatCurrency(amount, specificCurrency = null) {
    if (amount === null || amount === undefined) return ''

    // Use the specified currency, or fall back to the default/locale currency
    // Unwrap the reactive reference if it's a proxy object
    let currency = specificCurrency

    // Handle reactive references by checking if it's an object with a value property
    if (currency && typeof currency === 'object' && 'value' in currency) {
      currency = currency.value
    }

    // If no specific currency provided, use the default
    if (!currency) {
      currency = currencyCode.value
    }

    // Ensure we have a valid 3-letter currency code
    if (currency && currency.length !== 3) {
      // If it's a symbol like '$' or '€', try to find the corresponding code
      const currencyEntry = availableCurrencies.find(c => c.symbol === currency)
      if (currencyEntry) {
        currency = currencyEntry.code
      } else {
        // If we can't determine the currency code, default to USD
        console.warn(`Invalid currency code: ${currency}, defaulting to USD`)
        currency = 'USD'
      }
    }

    // Special cases for currencies where we want to customize the symbol display
    const specialCaseCurrencies = {
      'ZAR': { symbol: 'R', position: 'before' },
      'NGA': { symbol: '₦', position: 'before' },
      'BRL': { symbol: 'R$', position: 'before' },
      'PLN': { symbol: 'zł', position: 'after' },
      'HUF': { symbol: 'Ft', position: 'after' },
      'RON': { symbol: 'lei', position: 'after' }
    };

    if (specialCaseCurrencies[currency]) {
      try {
        // Format without currency symbol first
        const formatted = new Intl.NumberFormat(normalizeLocale(locale.value), {
          style: 'decimal',
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }).format(amount)

        // Then add the symbol in the correct position
        const { symbol, position } = specialCaseCurrencies[currency];
        return position === 'before' ? `${symbol}${formatted}` : `${formatted} ${symbol}`;
      } catch (e) {
        console.error(`Error formatting ${currency} currency:`, e)
      }
    }

    try {
      // For other currencies, use the standard Intl formatter with currency style
      const formatter = new Intl.NumberFormat(normalizeLocale(locale.value), {
        style: 'currency',
        currency: currency,
        currencyDisplay: 'symbol' // Explicitly request symbol display
      });

      return formatter.format(amount)
    } catch (e) {
      console.error('Error formatting currency:', e)
      // Try again with default locale if custom locale fails
      try {
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: currency,
          currencyDisplay: 'symbol' // Explicitly request symbol display
        }).format(amount)
      } catch (fallbackError) {
        console.error('Fallback currency formatting also failed:', fallbackError)
        // Fallback to basic formatting
        const symbol = currencySymbols[currency] || '$'
        return `${symbol}${parseFloat(amount).toFixed(2)}`
      }
    }
  }

  // Get symbol for a specific currency
  function getCurrencySymbol(code) {
    // Handle reactive references by checking if it's an object with a value property
    if (code && typeof code === 'object' && 'value' in code) {
      code = code.value
    }

    if (!code) return '$'

    // If it's already a valid 3-letter code
    if (currencySymbols[code]) {
      return currencySymbols[code]
    }

    // If it's a symbol like '$' or '€', return it directly
    if (code.length === 1 || (code.length <= 3 && !code.match(/^[A-Z]{3}$/))) {
      return code
    }

    // Default to $ if we can't determine the symbol
    return '$'
  }

  // Format a date
  function formatDate(dateString, options = {}) {
    if (!dateString) return ''

    try {
      const date = new Date(dateString)
      // Use normalized locale for consistent formatting
      return date.toLocaleDateString(normalizeLocale(locale.value), {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        ...options
      })
    } catch (e) {
      console.error('Error formatting date:', e)
      // Try again with default locale if custom locale fails
      try {
        const date = new Date(dateString)
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          ...options
        })
      } catch (fallbackError) {
        console.error('Fallback date formatting also failed:', fallbackError)
        return dateString
      }
    }
  }

  // Format a date and time
  function formatDateTime(dateTimeString, options = {}) {
    if (!dateTimeString) return ''

    try {
      const date = new Date(dateTimeString)
      // Use normalized locale for consistent formatting
      return date.toLocaleString(normalizeLocale(locale.value), {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric',
        ...options
      })
    } catch (e) {
      console.error('Error formatting date and time:', e)
      // Try again with default locale if custom locale fails
      try {
        const date = new Date(dateTimeString)
        return date.toLocaleString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: 'numeric',
          minute: 'numeric',
          ...options
        })
      } catch (fallbackError) {
        console.error('Fallback date-time formatting also failed:', fallbackError)
        return dateTimeString
      }
    }
  }

  // Load locale and default currency from user profile
  async function loadLocale(userId) {
    if (!userId) return

    try {
      loading.value = true
      error.value = null

      const { data, error: fetchError } = await supabase
        .from('profiles')
        .select('locale, default_currency')
        .eq('id', userId)
        .single()

      if (fetchError) throw fetchError

      if (data) {
        if (data.locale) {
          // Store the locale exactly as it is in the database
          // The formatting functions will handle normalization
          locale.value = data.locale

          // Check if this locale is in our available locales list
          const localeExists = availableLocales.some(l =>
            l.code === data.locale ||
            l.code === normalizeLocale(data.locale) ||
            normalizeLocale(l.code) === normalizeLocale(data.locale)
          )

          // If not found, try to find a close match or fall back to browser default
          if (!localeExists) {
            console.warn(`Locale ${data.locale} not found in available locales, using browser default`)
            locale.value = defaultLocale
          }
        }

        if (data.default_currency) {
          defaultCurrency.value = data.default_currency
        }

        // Mark as loaded
        isLoaded.value = true
      }

      return { locale: locale.value, defaultCurrency: defaultCurrency.value }
    } catch (e) {
      console.error('Error loading locale and currency:', e)
      error.value = e.message
      return { locale: defaultLocale, defaultCurrency: null }
    } finally {
      loading.value = false
    }
  }

  // Update locale in user profile
  async function updateLocale(userId, newLocale) {
    if (!userId) return false

    try {
      loading.value = true
      error.value = null

      // Check if the locale exists in our available locales
      const localeExists = availableLocales.some(l =>
        l.code === newLocale ||
        l.code === normalizeLocale(newLocale) ||
        normalizeLocale(l.code) === normalizeLocale(newLocale)
      )

      if (!localeExists) {
        console.warn(`Locale ${newLocale} not found in available locales, using browser default`)
        newLocale = defaultLocale
      }

      // Update locale in the store
      locale.value = newLocale

      // Update locale in the database
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ locale: newLocale })
        .eq('id', userId)

      if (updateError) throw updateError

      return true
    } catch (e) {
      console.error('Error updating locale:', e)
      error.value = e.message
      return false
    } finally {
      loading.value = false
    }
  }

  // Update default currency in user profile
  async function updateDefaultCurrency(userId, newCurrency) {
    if (!userId) return false

    try {
      loading.value = true
      error.value = null

      // Update currency in the store
      defaultCurrency.value = newCurrency

      // Update currency in the database
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ default_currency: newCurrency })
        .eq('id', userId)

      if (updateError) throw updateError

      return true
    } catch (e) {
      console.error('Error updating default currency:', e)
      error.value = e.message
      return false
    } finally {
      loading.value = false
    }
  }

  // Get available locales
  const availableLocales = [
    // English locales
    { code: 'en-US', name: 'English (United States)' },
    { code: 'en-GB', name: 'English (United Kingdom)' },
    { code: 'en-CA', name: 'English (Canada)' },
    { code: 'en-AU', name: 'English (Australia)' },
    { code: 'en-NZ', name: 'English (New Zealand)' },
    { code: 'en-IE', name: 'English (Ireland)' },
    { code: 'en-ZA', name: 'English (South Africa)' },
    { code: 'en-IN', name: 'English (India)' },
    { code: 'en-SG', name: 'English (Singapore)' },
    { code: 'en-HK', name: 'English (Hong Kong)' },
    { code: 'en-PH', name: 'English (Philippines)' },

    // Spanish locales
    { code: 'es-ES', name: 'Spanish (Spain)' },
    { code: 'es-MX', name: 'Spanish (Mexico)' },
    { code: 'es-AR', name: 'Spanish (Argentina)' },
    { code: 'es-CO', name: 'Spanish (Colombia)' },
    { code: 'es-CL', name: 'Spanish (Chile)' },
    { code: 'es-PE', name: 'Spanish (Peru)' },
    { code: 'es-VE', name: 'Spanish (Venezuela)' },
    { code: 'es-EC', name: 'Spanish (Ecuador)' },
    { code: 'es-GT', name: 'Spanish (Guatemala)' },
    { code: 'es-CR', name: 'Spanish (Costa Rica)' },
    { code: 'es-PR', name: 'Spanish (Puerto Rico)' },
    { code: 'es-DO', name: 'Spanish (Dominican Republic)' },
    { code: 'es-UY', name: 'Spanish (Uruguay)' },
    { code: 'es-PA', name: 'Spanish (Panama)' },

    // French locales
    { code: 'fr-FR', name: 'French (France)' },
    { code: 'fr-CA', name: 'French (Canada)' },
    { code: 'fr-BE', name: 'French (Belgium)' },
    { code: 'fr-CH', name: 'French (Switzerland)' },
    { code: 'fr-LU', name: 'French (Luxembourg)' },

    // German locales
    { code: 'de-DE', name: 'German (Germany)' },
    { code: 'de-AT', name: 'German (Austria)' },
    { code: 'de-CH', name: 'German (Switzerland)' },
    { code: 'de-LU', name: 'German (Luxembourg)' },
    { code: 'de-LI', name: 'German (Liechtenstein)' },

    // Portuguese locales
    { code: 'pt-BR', name: 'Portuguese (Brazil)' },
    { code: 'pt-PT', name: 'Portuguese (Portugal)' },
    { code: 'pt-AO', name: 'Portuguese (Angola)' },
    { code: 'pt-MZ', name: 'Portuguese (Mozambique)' },

    // Italian locales
    { code: 'it-IT', name: 'Italian (Italy)' },
    { code: 'it-CH', name: 'Italian (Switzerland)' },
    { code: 'it-SM', name: 'Italian (San Marino)' },

    // Dutch locales
    { code: 'nl-NL', name: 'Dutch (Netherlands)' },
    { code: 'nl-BE', name: 'Dutch (Belgium)' },

    // Scandinavian locales
    { code: 'sv-SE', name: 'Swedish (Sweden)' },
    { code: 'da-DK', name: 'Danish (Denmark)' },
    { code: 'no-NO', name: 'Norwegian (Norway)' },
    { code: 'fi-FI', name: 'Finnish (Finland)' },
    { code: 'is-IS', name: 'Icelandic (Iceland)' },

    // Eastern European locales
    { code: 'pl-PL', name: 'Polish (Poland)' },
    { code: 'cs-CZ', name: 'Czech (Czech Republic)' },
    { code: 'sk-SK', name: 'Slovak (Slovakia)' },
    { code: 'hu-HU', name: 'Hungarian (Hungary)' },
    { code: 'ro-RO', name: 'Romanian (Romania)' },
    { code: 'bg-BG', name: 'Bulgarian (Bulgaria)' },
    { code: 'hr-HR', name: 'Croatian (Croatia)' },
    { code: 'sl-SI', name: 'Slovenian (Slovenia)' },
    { code: 'sr-RS', name: 'Serbian (Serbia)' },
    { code: 'uk-UA', name: 'Ukrainian (Ukraine)' },

    // Baltic locales
    { code: 'lt-LT', name: 'Lithuanian (Lithuania)' },
    { code: 'lv-LV', name: 'Latvian (Latvia)' },
    { code: 'et-EE', name: 'Estonian (Estonia)' },

    // Other European locales
    { code: 'el-GR', name: 'Greek (Greece)' },
    { code: 'tr-TR', name: 'Turkish (Turkey)' },

    // East Asian locales
    { code: 'ja-JP', name: 'Japanese (Japan)' },
    { code: 'ko-KR', name: 'Korean (South Korea)' },
    { code: 'zh-CN', name: 'Chinese (China)' },
    { code: 'zh-TW', name: 'Chinese (Taiwan)' },
    { code: 'zh-HK', name: 'Chinese (Hong Kong)' },
    { code: 'zh-SG', name: 'Chinese (Singapore)' },

    // Southeast Asian locales
    { code: 'th-TH', name: 'Thai (Thailand)' },
    { code: 'vi-VN', name: 'Vietnamese (Vietnam)' },
    { code: 'id-ID', name: 'Indonesian (Indonesia)' },
    { code: 'ms-MY', name: 'Malay (Malaysia)' },
    { code: 'fil-PH', name: 'Filipino (Philippines)' },

    // South Asian locales
    { code: 'hi-IN', name: 'Hindi (India)' },
    { code: 'bn-IN', name: 'Bengali (India)' },
    { code: 'bn-BD', name: 'Bengali (Bangladesh)' },
    { code: 'ta-IN', name: 'Tamil (India)' },
    { code: 'ta-LK', name: 'Tamil (Sri Lanka)' },
    { code: 'ur-PK', name: 'Urdu (Pakistan)' },

    // Middle Eastern locales
    { code: 'ar-SA', name: 'Arabic (Saudi Arabia)' },
    { code: 'ar-EG', name: 'Arabic (Egypt)' },
    { code: 'ar-AE', name: 'Arabic (United Arab Emirates)' },
    { code: 'ar-QA', name: 'Arabic (Qatar)' },
    { code: 'ar-KW', name: 'Arabic (Kuwait)' },
    { code: 'ar-MA', name: 'Arabic (Morocco)' },
    { code: 'ar-DZ', name: 'Arabic (Algeria)' },
    { code: 'ar-TN', name: 'Arabic (Tunisia)' },
    { code: 'ar-LB', name: 'Arabic (Lebanon)' },
    { code: 'ar-JO', name: 'Arabic (Jordan)' },
    { code: 'he-IL', name: 'Hebrew (Israel)' },
    { code: 'fa-IR', name: 'Persian (Iran)' },

    // African locales
    { code: 'af-ZA', name: 'Afrikaans (South Africa)' },
    { code: 'af_ZA', name: 'Afrikaans (South Africa) [underscore]' },
    { code: 'zu-ZA', name: 'Zulu (South Africa)' },
    { code: 'zu_ZA', name: 'Zulu (South Africa) [underscore]' },
    { code: 'xh-ZA', name: 'Xhosa (South Africa)' },
    { code: 'xh_ZA', name: 'Xhosa (South Africa) [underscore]' },
    { code: 'st-ZA', name: 'Sesotho (South Africa)' },
    { code: 'st_ZA', name: 'Sesotho (South Africa) [underscore]' },
    { code: 'tn-ZA', name: 'Tswana (South Africa)' },
    { code: 'tn_ZA', name: 'Tswana (South Africa) [underscore]' },
    { code: 'nso-ZA', name: 'Northern Sotho (South Africa)' },
    { code: 'nso_ZA', name: 'Northern Sotho (South Africa) [underscore]' },
    { code: 'sw-KE', name: 'Swahili (Kenya)' },
    { code: 'sw_KE', name: 'Swahili (Kenya) [underscore]' },
    { code: 'sw-TZ', name: 'Swahili (Tanzania)' },
    { code: 'sw_TZ', name: 'Swahili (Tanzania) [underscore]' },
    { code: 'am-ET', name: 'Amharic (Ethiopia)' },
    { code: 'am_ET', name: 'Amharic (Ethiopia) [underscore]' },
    { code: 'ha-NG', name: 'Hausa (Nigeria)' },
    { code: 'ha_NG', name: 'Hausa (Nigeria) [underscore]' },
    { code: 'yo-NG', name: 'Yoruba (Nigeria)' },
    { code: 'yo_NG', name: 'Yoruba (Nigeria) [underscore]' },
    { code: 'ig-NG', name: 'Igbo (Nigeria)' },
    { code: 'ig_NG', name: 'Igbo (Nigeria) [underscore]' },
    { code: 'so-SO', name: 'Somali (Somalia)' },
    { code: 'so_SO', name: 'Somali (Somalia) [underscore]' },
    { code: 'rw-RW', name: 'Kinyarwanda (Rwanda)' },
    { code: 'rw_RW', name: 'Kinyarwanda (Rwanda) [underscore]' },
    { code: 'mg-MG', name: 'Malagasy (Madagascar)' },
    { code: 'mg_MG', name: 'Malagasy (Madagascar) [underscore]' },

    // Other major locales
    { code: 'ru-RU', name: 'Russian (Russia)' },
    { code: 'ru_RU', name: 'Russian (Russia) [underscore]' },
    { code: 'kk-KZ', name: 'Kazakh (Kazakhstan)' },
    { code: 'kk_KZ', name: 'Kazakh (Kazakhstan) [underscore]' },

    // Additional underscore format locales for major languages
    { code: 'en_US', name: 'English (United States) [underscore]' },
    { code: 'en_GB', name: 'English (United Kingdom) [underscore]' },
    { code: 'en_CA', name: 'English (Canada) [underscore]' },
    { code: 'en_AU', name: 'English (Australia) [underscore]' },
    { code: 'en_NZ', name: 'English (New Zealand) [underscore]' },
    { code: 'en_ZA', name: 'English (South Africa) [underscore]' },
    { code: 'es_ES', name: 'Spanish (Spain) [underscore]' },
    { code: 'es_MX', name: 'Spanish (Mexico) [underscore]' },
    { code: 'fr_FR', name: 'French (France) [underscore]' },
    { code: 'fr_CA', name: 'French (Canada) [underscore]' },
    { code: 'de_DE', name: 'German (Germany) [underscore]' },
    { code: 'it_IT', name: 'Italian (Italy) [underscore]' },
    { code: 'pt_BR', name: 'Portuguese (Brazil) [underscore]' },
    { code: 'pt_PT', name: 'Portuguese (Portugal) [underscore]' },
    { code: 'ja_JP', name: 'Japanese (Japan) [underscore]' },
    { code: 'zh_CN', name: 'Chinese (China) [underscore]' },
    { code: 'zh_TW', name: 'Chinese (Taiwan) [underscore]' },
    { code: 'ko_KR', name: 'Korean (South Korea) [underscore]' },
    { code: 'ar_SA', name: 'Arabic (Saudi Arabia) [underscore]' },
    { code: 'hi_IN', name: 'Hindi (India) [underscore]' }
  ]

  // Get default currency for a specific locale
  function getDefaultCurrencyForLocale(localeString) {
    // Handle reactive references by checking if it's an object with a value property
    if (localeString && typeof localeString === 'object' && 'value' in localeString) {
      localeString = localeString.value
    }

    // Normalize the locale to hyphen format
    const normalizedLocale = normalizeLocale(localeString)

    // Try to get currency from locale map
    if (localeToCurrency[normalizedLocale]) {
      return localeToCurrency[normalizedLocale]
    }

    // For other locales, try to guess based on region code
    const regionCode = normalizedLocale.split('-')[1]
    if (regionCode) {
      // Region to currency mappings
      // North America
      if (regionCode === 'US') return 'USD'
      if (regionCode === 'CA') return 'CAD'
      if (regionCode === 'MX') return 'MXN'

      // Europe
      if (regionCode === 'GB') return 'GBP'
      if (['FR', 'DE', 'IT', 'ES', 'PT', 'NL', 'BE', 'AT', 'FI', 'IE', 'GR', 'SK', 'SI', 'LV', 'LT', 'EE', 'MT', 'CY', 'LU'].includes(regionCode)) {
        return 'EUR' // Euro zone countries
      }
      // ... other region mappings as in the currencyCode computed property
    }

    // Default to USD if we can't determine the currency
    return 'USD'
  }

  return {
    locale,
    defaultLocale,
    defaultCurrency,
    loading,
    error,
    currencyCode,
    currencySymbol,
    formatCurrency,
    formatDate,
    formatDateTime,
    loadLocale,
    updateLocale,
    updateDefaultCurrency,
    getCurrencySymbol,
    availableCurrencies,
    availableLocales,
    getDefaultCurrencyForLocale,
    isLoaded
  }
})
