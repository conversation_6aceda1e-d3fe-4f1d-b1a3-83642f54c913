import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { supabase } from '@/lib/supabase'
import { NotificationService } from '@/services/NotificationService'
import { Capacitor } from '@capacitor/core'
import { useAuth } from '@/composables/useAuth'

export const useNotificationsStore = defineStore('notifications', () => {
  // State
  const notifications = ref([])
  const isEnabled = ref(false)
  const loading = ref(false)
  const error = ref(null)
  const deviceToken = ref(null)
  const snackbar = ref({
    show: false,
    text: '',
    color: 'success'
  })

  // Composables
  const authStore = useAuth()

  // Getters
  const unreadCount = computed(() => {
    return notifications.value.filter(n => !n.read).length
  })

  // Actions
  /**
   * Initialize notifications
   * @param {string} userId - The user ID
   */
  async function initialize(userId) {
    console.log('Initializing notifications for user:', userId)
    loading.value = true
    error.value = null

    try {
      // Check if web notifications are already granted
      const webNotificationsAvailable = NotificationService.isWebNotificationAvailable()
      const webNotificationsGranted = webNotificationsAvailable && Notification.permission === 'granted'
      console.log('Web notifications granted:', webNotificationsGranted)

      // Check if push notifications are available
      const pushAvailable = NotificationService.isPushAvailable()
      console.log('Push notifications available:', pushAvailable)

      // Load notification settings from local storage
      const storedSettings = localStorage.getItem('notificationSettings')
      if (storedSettings) {
        const parsedSettings = JSON.parse(storedSettings)
        console.log('Loaded settings from localStorage:', parsedSettings)
        
        // Only use stored settings if browser permission matches
        if (parsedSettings.enabled && Notification.permission === 'granted') {
          isEnabled.value = true
        } else if (!parsedSettings.enabled) {
          isEnabled.value = false
        }
      } else {
        // If no stored settings, use browser permission status
        isEnabled.value = Notification.permission === 'granted'
      }

      // Try to load notification preferences from Supabase
      if (userId) {
        console.log('Loading notification preferences from Supabase')
        const { data, error: prefError } = await supabase
          .from('notification_preferences')
          .select('*')
          .eq('user_id', userId)
          .single()

        if (!prefError && data) {
          console.log('Loaded preferences from Supabase:', data)
          // Use the server-side setting
          isEnabled.value = data.enabled

          // Update local storage
          localStorage.setItem('notificationSettings', JSON.stringify({
            enabled: data.enabled
          }))
        } else {
          console.log('No preferences found in Supabase, creating default')

          // If web notifications are granted or push is available, enable by default
          if (webNotificationsGranted || pushAvailable) {
            isEnabled.value = true

            // Create default preferences
            const { error: insertError } = await supabase
              .from('notification_preferences')
              .upsert({
                user_id: userId,
                enabled: true,
                birthday_reminders: true,
                wish_updates: true,
                friend_activity: true
              })

            if (insertError) {
              console.error('Error creating default preferences:', insertError)
            } else {
              console.log('Created default preferences with notifications enabled')

              // Update local storage
              localStorage.setItem('notificationSettings', JSON.stringify({
                enabled: true
              }))
            }
          }
        }
      }

      console.log('Notifications enabled state after initialization:', isEnabled.value)

      // Initialize appropriate notification system
      if (NotificationService.isPushAvailable()) {
        // Mobile push notifications
        if (isEnabled.value) {
          console.log('Initializing push notifications')
          await NotificationService.initializePushNotifications()
          await NotificationService.setupPushListeners(
            handleNotificationReceived,
            handleTokenReceived
          )
        }
      } else if (NotificationService.isWebNotificationAvailable()) {
        // Web notifications
        if (isEnabled.value) {
          console.log('Initializing web notifications')
          await NotificationService.initializeWebNotifications()
        }
      }

      // Load notifications from Supabase
      await loadNotifications(userId)
    } catch (err) {
      console.error('Error initializing notifications:', err)
      error.value = 'Failed to initialize notifications'
    } finally {
      loading.value = false
    }
  }

  /**
   * Load notifications from Supabase
   * @param {string} userId - The user ID
   */
  async function loadNotifications(userId) {
    if (!userId) return

    loading.value = true
    error.value = null

    try {
      const { data, error: err } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(20)

      if (err) {
        throw err
      }

      notifications.value = data || []
    } catch (err) {
      console.error('Error loading notifications:', err)
      error.value = 'Failed to load notifications'
    } finally {
      loading.value = false
    }
  }

  /**
   * Mark a notification as read
   * @param {string} notificationId - The notification ID
   */
  async function markAsRead(notificationId) {
    try {
      // Update local state
      const notification = notifications.value.find(n => n.id === notificationId)
      if (notification) {
        notification.read = true
      }

      // Update in Supabase
      await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId)
    } catch (err) {
      console.error('Error marking notification as read:', err)
    }
  }

  /**
   * Mark all notifications as read
   * @param {string} userId - The user ID
   */
  async function markAllAsRead(userId) {
    try {
      // Update local state
      notifications.value.forEach(n => {
        n.read = true
      })

      // Update in Supabase
      await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', userId)
    } catch (err) {
      console.error('Error marking all notifications as read:', err)
    }
  }

  /**
   * Toggle notifications on/off
   * @param {boolean} enabled - Whether notifications are enabled
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} Result of the operation
   */
  async function toggleNotifications(enabled, userId) {
    console.log('toggleNotifications called with:', { enabled, userId })

    const result = {
      success: false,
      permissionGranted: false,
      error: null
    }

    // Validate inputs
    if (typeof enabled !== 'boolean') {
      console.error('Invalid enabled value:', enabled)
      result.error = 'Invalid enabled value'
      return result
    }

    if (!userId) {
      console.error('No userId provided to toggleNotifications')
      result.error = 'No userId provided'
      return result
    }

    // Update local state
    console.log('Setting isEnabled.value to:', enabled)
    isEnabled.value = enabled

    // Save to local storage
    console.log('Saving to localStorage')
    localStorage.setItem('notificationSettings', JSON.stringify({
      enabled: enabled
    }))

    if (enabled) {
      console.log('Notifications enabled, initializing notification system')

      // Check if push notifications are available
      const pushAvailable = NotificationService.isPushAvailable()
      console.log('Push notifications available:', pushAvailable)

      // Check if web notifications are available
      const webNotificationsAvailable = NotificationService.isWebNotificationAvailable()
      console.log('Web notifications available:', webNotificationsAvailable)

      // Initialize appropriate notification system
      if (pushAvailable) {
        console.log('Initializing push notifications...')
        try {
          await NotificationService.initializePushNotifications()
          console.log('Push notifications initialized successfully')

          console.log('Setting up push listeners...')
          await NotificationService.setupPushListeners(
            handleNotificationReceived,
            handleTokenReceived
          )
          console.log('Push listeners set up successfully')
        } catch (error) {
          console.error('Error initializing push notifications:', error)
        }
      } else if (webNotificationsAvailable) {
        console.log('Initializing web notifications...')
        try {
          const permissionResult = await NotificationService.initializeWebNotifications(userId)
          console.log('Web notifications initialized with result:', permissionResult)

          // Update our success flag based on permission result
          result.permissionGranted = permissionResult;

          // Only send a test notification if permission was granted
          if (permissionResult) {
            console.log('Sending test web notification...')
            await NotificationService.sendWebNotification({
              title: 'Notifications Enabled',
              body: 'You will now receive notifications from ConfettiWish',
              data: { url: '/' }
            })
          } else {
            // If permission was denied, we should update our local state to reflect that
            console.log('Notification permission denied, updating state to disabled')
            isEnabled.value = false
            localStorage.setItem('notificationSettings', JSON.stringify({
              enabled: false
            }))
            result.error = 'Notification permission denied by user'
          }
        } catch (error) {
          console.error('Error initializing web notifications:', error)
        }
      } else {
        console.log('No notification system available on this device/browser')
      }
    } else {
      console.log('Notifications disabled')
    }

    // Update notification preferences in Supabase
    try {
      console.log('Updating notification preferences in Supabase')
      const { data, error } = await supabase
        .from('notification_preferences')
        .upsert({
          user_id: userId,
          enabled: enabled,
          birthday_reminders: true,
          wish_updates: true,
          friend_activity: true
        }, { onConflict: 'user_id' })
        .select()

      if (error) {
        console.error('Error updating notification preferences:', error)
        result.error = error.message
      } else {
        console.log('Successfully updated notification preferences:', data)
        
        // Only mark as successful if we didn't hit permission issues earlier
        if (!result.error) {
          result.success = true
        }

        // Check if web notifications permission was granted
        if (NotificationService.isWebNotificationAvailable()) {
          result.permissionGranted = Notification.permission === 'granted'
          
          // If notifications are enabled but permission was denied
          if (enabled && Notification.permission === 'denied') {
            result.error = 'Notification permission denied by browser'
            result.success = false
          }
        } else if (NotificationService.isPushAvailable()) {
          // For mobile, we assume permission is granted if we got this far
          result.permissionGranted = true
        }
      }
    } catch (err) {
      console.error('Error updating notification preferences:', err)
      result.error = err.message
    }

    return result
  }

  /**
   * Handle notification received
   * @param {Object} notification - The notification object
   */
  function handleNotificationReceived(notification) {
    // Add to local notifications array
    notifications.value.unshift({
      id: notification.id || `local-${Date.now()}`,
      title: notification.title,
      body: notification.body,
      data: notification.data,
      read: false,
      created_at: new Date().toISOString()
    })
  }

  /**
   * Handle token received
   * @param {string} token - The device token
   */
  async function handleTokenReceived(token) {
    deviceToken.value = token

    // Get current user
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return

    // Register token with backend
    const platform = Capacitor.getPlatform()
    await NotificationService.registerDeviceToken(token, platform, user.id)
  }

  /**
   * Send a test notification
   */
  async function sendTestNotification() {
    if (!authStore.user) {
      console.error('No user found when sending test notification')
      return
    }

    try {
      const success = await NotificationService.sendTestNotification(authStore.user.id)
      if (success) {
        snackbar.value = {
          show: true,
          text: 'Test notification sent successfully',
          color: 'success'
        }
      } else {
        snackbar.value = {
          show: true,
          text: 'Failed to send test notification',
          color: 'error'
        }
      }
    } catch (error) {
      console.error('Error sending test notification:', error)
      snackbar.value = {
        show: true,
        text: 'Error sending test notification',
        color: 'error'
      }
    }
  }

  return {
    // State
    notifications,
    isEnabled,
    loading,
    error,
    deviceToken,

    // Getters
    unreadCount,

    // Actions
    initialize,
    loadNotifications,
    markAsRead,
    markAllAsRead,
    toggleNotifications,
    sendTestNotification
  }
})
