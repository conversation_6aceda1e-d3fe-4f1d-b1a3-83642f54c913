// Global type declarations for browser APIs

declare global {
  interface Window {
    Notification: typeof Notification;
  }

  interface NotificationOptions {
    body?: string;
    icon?: string;
    badge?: string;
    data?: Record<string, unknown>;
    tag?: string;
    requireInteraction?: boolean;
    silent?: boolean;
    vibrate?: number[];
  }

  class Notification {
    static permission: 'default' | 'granted' | 'denied';
    static requestPermission(): Promise<'default' | 'granted' | 'denied'>;

    constructor(title: string, options?: NotificationOptions);

    close(): void;
    onclick: ((this: Notification, ev: Event) => void) | null;
    onclose: ((this: Notification, ev: Event) => void) | null;
    onerror: ((this: Notification, ev: Event) => void) | null;
    onshow: ((this: Notification, ev: Event) => void) | null;
  }
}

export {};
