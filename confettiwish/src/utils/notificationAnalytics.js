import { supabase } from '@/lib/supabase'

/**
 * Utility for tracking notification analytics
 */
export class NotificationAnalytics {
  /**
   * Track notification received
   * @param {Object} notification - Notification object
   * @param {string} userId - User ID
   * @returns {Promise<void>}
   */
  static async trackReceived(notification, userId) {
    if (!notification || !userId) return
    
    try {
      // Store analytics event
      await supabase
        .from('notification_analytics')
        .insert({
          user_id: userId,
          notification_id: notification.id,
          event_type: 'received',
          notification_type: notification.type || 'unknown',
          platform: this.getPlatform(),
          metadata: {
            title: notification.title,
            timestamp: new Date().toISOString()
          }
        })
    } catch (error) {
      console.error('Error tracking notification received:', error)
    }
  }
  
  /**
   * Track notification clicked
   * @param {Object} notification - Notification object
   * @param {string} userId - User ID
   * @param {string} [action] - Action taken (if any)
   * @returns {Promise<void>}
   */
  static async trackClicked(notification, userId, action = 'default') {
    if (!notification || !userId) return
    
    try {
      // Store analytics event
      await supabase
        .from('notification_analytics')
        .insert({
          user_id: userId,
          notification_id: notification.id,
          event_type: 'clicked',
          notification_type: notification.type || 'unknown',
          platform: this.getPlatform(),
          metadata: {
            title: notification.title,
            action,
            timestamp: new Date().toISOString()
          }
        })
    } catch (error) {
      console.error('Error tracking notification clicked:', error)
    }
  }
  
  /**
   * Track notification error
   * @param {Object} notification - Notification object
   * @param {string} userId - User ID
   * @param {string} errorMessage - Error message
   * @returns {Promise<void>}
   */
  static async trackError(notification, userId, errorMessage) {
    if (!userId) return
    
    try {
      // Store analytics event
      await supabase
        .from('notification_analytics')
        .insert({
          user_id: userId,
          notification_id: notification?.id,
          event_type: 'error',
          notification_type: notification?.type || 'unknown',
          platform: this.getPlatform(),
          metadata: {
            title: notification?.title,
            error: errorMessage,
            timestamp: new Date().toISOString()
          }
        })
    } catch (error) {
      console.error('Error tracking notification error:', error)
    }
  }
  
  /**
   * Get platform information
   * @returns {string} Platform name
   */
  static getPlatform() {
    // Check if running in Capacitor
    if (window.Capacitor && window.Capacitor.isNativePlatform()) {
      return window.Capacitor.getPlatform()
    }
    
    // Web platform
    return 'web'
  }
  
  /**
   * Get notification analytics for a user
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @param {number} [options.limit=100] - Maximum number of records to return
   * @param {string} [options.eventType] - Filter by event type
   * @returns {Promise<Array>} Analytics data
   */
  static async getAnalytics(userId, options = {}) {
    if (!userId) return []
    
    const { limit = 100, eventType } = options
    
    try {
      let query = supabase
        .from('notification_analytics')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit)
      
      // Add event type filter if provided
      if (eventType) {
        query = query.eq('event_type', eventType)
      }
      
      const { data, error } = await query
      
      if (error) {
        console.error('Error fetching notification analytics:', error)
        return []
      }
      
      return data || []
    } catch (error) {
      console.error('Error getting notification analytics:', error)
      return []
    }
  }
  
  /**
   * Get notification open rate
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @param {string} [options.timeframe='30d'] - Timeframe (1d, 7d, 30d, all)
   * @returns {Promise<Object>} Open rate statistics
   */
  static async getOpenRate(userId, options = {}) {
    if (!userId) return { openRate: 0, received: 0, clicked: 0 }
    
    const { timeframe = '30d' } = options
    
    try {
      // Calculate date range
      const now = new Date()
      let startDate = new Date()
      
      switch (timeframe) {
        case '1d':
          startDate.setDate(now.getDate() - 1)
          break
        case '7d':
          startDate.setDate(now.getDate() - 7)
          break
        case '30d':
          startDate.setDate(now.getDate() - 30)
          break
        case 'all':
          startDate = new Date(0) // Beginning of time
          break
        default:
          startDate.setDate(now.getDate() - 30)
      }
      
      // Get received count
      const { count: receivedCount, error: receivedError } = await supabase
        .from('notification_analytics')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('event_type', 'received')
        .gte('created_at', startDate.toISOString())
      
      if (receivedError) {
        console.error('Error getting received count:', receivedError)
        return { openRate: 0, received: 0, clicked: 0 }
      }
      
      // Get clicked count
      const { count: clickedCount, error: clickedError } = await supabase
        .from('notification_analytics')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('event_type', 'clicked')
        .gte('created_at', startDate.toISOString())
      
      if (clickedError) {
        console.error('Error getting clicked count:', clickedError)
        return { openRate: 0, received: receivedCount, clicked: 0 }
      }
      
      // Calculate open rate
      const openRate = receivedCount > 0 ? (clickedCount / receivedCount) * 100 : 0
      
      return {
        openRate,
        received: receivedCount,
        clicked: clickedCount
      }
    } catch (error) {
      console.error('Error calculating open rate:', error)
      return { openRate: 0, received: 0, clicked: 0 }
    }
  }
}
