/**
 * Notification templates for different types of notifications
 */
export const NotificationTemplates = {
  /**
   * Birthday reminder notification
   * @param {Object} params - Parameters for the notification
   * @param {string} params.name - Friend's name
   * @param {number} params.days - Days until birthday
   * @param {number} [params.age] - Age the friend will be turning
   * @param {string} params.friendId - Friend ID
   * @returns {Object} Notification data
   */
  birthdayReminder(params) {
    const { name, days, age, friendId } = params
    
    // Age text if available
    const ageText = age ? ` (turning ${age})` : ''
    
    // Title based on days
    let title = ''
    if (days === 0) {
      title = `🎂 Birthday Today!`
    } else if (days === 1) {
      title = `🎂 Birthday Tomorrow!`
    } else {
      title = `🎂 Birthday in ${days} days`
    }
    
    // Body based on days
    let body = ''
    if (days === 0) {
      body = `${name}${ageText} has a birthday today!`
    } else if (days === 1) {
      body = `${name}${ageText} has a birthday tomorrow!`
    } else {
      body = `${name}${ageText} has a birthday in ${days} days.`
    }
    
    return {
      title,
      body,
      data: {
        url: `/friends/${friendId}`,
        friendId,
        days,
        notificationKey: `birthday_${friendId}_${new Date().getFullYear()}_${days}days`
      }
    }
  },
  
  /**
   * Wish update notification
   * @param {Object} params - Parameters for the notification
   * @param {string} params.friendName - Friend's name
   * @param {string} params.wishTitle - Wish title
   * @param {boolean} params.isPurchased - Whether the wish was purchased
   * @param {string} params.friendId - Friend ID
   * @param {string} params.wishId - Wish ID
   * @returns {Object} Notification data
   */
  wishUpdate(params) {
    const { friendName, wishTitle, isPurchased, friendId, wishId } = params
    
    const title = isPurchased 
      ? `🎁 Wish Purchased` 
      : `🎁 Wish Updated`
    
    const body = isPurchased
      ? `"${wishTitle}" for ${friendName} was marked as purchased.`
      : `"${wishTitle}" for ${friendName} was updated.`
    
    return {
      title,
      body,
      data: {
        url: `/friends/${friendId}/wishes`,
        friendId,
        wishId,
        isPurchased
      }
    }
  },
  
  /**
   * Friend activity notification
   * @param {Object} params - Parameters for the notification
   * @param {string} params.friendName - Friend's name
   * @param {string} params.action - Action performed (added, updated, etc.)
   * @param {string} params.friendId - Friend ID
   * @returns {Object} Notification data
   */
  friendActivity(params) {
    const { friendName, action, friendId } = params
    
    let title = ''
    let body = ''
    
    switch (action) {
      case 'added':
        title = `👋 New Friend Added`
        body = `${friendName} was added to your friends.`
        break
      case 'updated':
        title = `✏️ Friend Updated`
        body = `${friendName}'s information was updated.`
        break
      case 'imported':
        title = `📲 Friend Imported`
        body = `${friendName} was imported from your contacts.`
        break
      default:
        title = `👥 Friend Activity`
        body = `There was activity related to ${friendName}.`
    }
    
    return {
      title,
      body,
      data: {
        url: `/friends/${friendId}`,
        friendId,
        action
      }
    }
  },
  
  /**
   * System notification
   * @param {Object} params - Parameters for the notification
   * @param {string} params.title - Notification title
   * @param {string} params.body - Notification body
   * @param {Object} [params.data] - Additional data
   * @returns {Object} Notification data
   */
  system(params) {
    const { title, body, data = {} } = params
    
    return {
      title,
      body,
      data: {
        url: data.url || '/',
        ...data
      }
    }
  },
  
  /**
   * Test notification
   * @returns {Object} Notification data
   */
  test() {
    return {
      title: 'Test Notification',
      body: 'This is a test notification from ConfettiWish',
      data: {
        url: '/',
        test: true,
        timestamp: new Date().toISOString()
      }
    }
  }
}
