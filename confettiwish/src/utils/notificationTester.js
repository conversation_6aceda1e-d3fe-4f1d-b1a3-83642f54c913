import { NotificationService } from '@/services/NotificationService'
import { supabase } from '@/lib/supabase'

/**
 * Utility for testing notifications
 */
export class NotificationTester {
  /**
   * Test local notifications
   * @param {Object} options - Test options
   * @returns {Promise<Object>} Test results
   */
  static async testLocalNotification(options = {}) {
    const results = {
      success: false,
      platform: null,
      error: null,
      details: {}
    }

    try {
      // Determine platform
      if (NotificationService.isPushAvailable()) {
        results.platform = 'mobile'
        results.details.pushAvailable = true

        // Create test notification
        const notification = {
          title: options.title || 'Test Notification',
          body: options.body || 'This is a test notification from ConfettiWish',
          id: Date.now(),
          data: options.data || { url: '/' }
        }

        // Send local notification
        await NotificationService.sendLocalNotification(notification)
        results.success = true
        results.details.notification = notification
      } else if (NotificationService.isWebNotificationAvailable()) {
        results.platform = 'web'
        results.details.webNotificationAvailable = true

        // Check permission
        if (Notification.permission !== 'granted') {
          const permission = await Notification.requestPermission()
          results.details.permissionRequested = true
          results.details.permissionResult = permission

          if (permission !== 'granted') {
            throw new Error('Notification permission not granted')
          }
        }

        // Create test notification
        const notification = {
          title: options.title || 'Test Notification',
          body: options.body || 'This is a test notification from ConfettiWish',
          data: options.data || { url: '/' }
        }

        // Send web notification
        await NotificationService.sendWebNotification(notification)
        results.success = true
        results.details.notification = notification
      } else {
        throw new Error('Notifications not supported on this platform')
      }
    } catch (error) {
      results.success = false
      results.error = error.message
      results.details.errorStack = error.stack
    }

    return results
  }

  /**
   * Test push notifications via backend
   * @param {string} userId - User ID
   * @param {Object} options - Test options
   * @returns {Promise<Object>} Test results
   */
  static async testPushNotification(userId, options = {}) {
    const results = {
      success: false,
      error: null,
      details: {}
    }

    try {
      if (!userId) {
        throw new Error('User ID is required')
      }

      // Call the send-notifications function
      const { data, error } = await supabase.functions.invoke('send-notifications', {
        body: {
          type: 'test',
          userId: userId,
          data: {
            title: options.title || 'Test Push Notification',
            body: options.body || 'This is a test push notification from ConfettiWish',
            data: options.data || { 
              url: '/',
              test: true,
              timestamp: new Date().toISOString()
            }
          }
        }
      })

      if (error) {
        throw error
      }

      results.success = true
      results.details.response = data
    } catch (error) {
      results.success = false
      results.error = error.message
      results.details.errorObject = error
    }

    return results
  }

  /**
   * Verify device token registration
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Verification results
   */
  static async verifyDeviceTokenRegistration(userId) {
    const results = {
      success: false,
      tokensFound: 0,
      error: null,
      details: {}
    }

    try {
      if (!userId) {
        throw new Error('User ID is required')
      }

      // Check if device token is registered
      const { data, error } = await supabase
        .from('device_tokens')
        .select('id, token, platform, created_at')
        .eq('user_id', userId)

      if (error) {
        throw error
      }

      results.success = true
      results.tokensFound = data?.length || 0
      results.details.tokens = data
    } catch (error) {
      results.success = false
      results.error = error.message
    }

    return results
  }

  /**
   * Run all tests
   * @param {string} userId - User ID
   * @returns {Promise<Object>} All test results
   */
  static async runAllTests(userId) {
    const results = {
      localNotification: null,
      pushNotification: null,
      deviceTokenRegistration: null,
      timestamp: new Date().toISOString()
    }

    // Test local notification
    results.localNotification = await this.testLocalNotification()

    // Test device token registration
    if (userId) {
      results.deviceTokenRegistration = await this.verifyDeviceTokenRegistration(userId)

      // Test push notification if tokens are registered
      if (results.deviceTokenRegistration.tokensFound > 0) {
        results.pushNotification = await this.testPushNotification(userId)
      }
    }

    return results
  }
}
