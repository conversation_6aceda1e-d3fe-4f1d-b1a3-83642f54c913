<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useFriends } from '@/composables/useFriends'
import FriendCard from '@/components/FriendCard.vue'
import DeleteConfirmationDialog from '@/components/DeleteConfirmationDialog.vue'
import { supabase } from '@/lib/supabase'

const router = useRouter()
const friendsStore = useFriends()

// State variables
const loading = ref(false)
const searchQuery = ref('')
const searchTimeout = ref(null)
const showDeleteDialog = ref(false)
const friendToDelete = ref(null)
const deleteLoading = ref(false)
const showOnlyWithBirthday = ref(false)
const filteredFriends = ref([]) // Add this new ref to store filtered friends

// Snackbar for notifications
const snackbar = ref({
  show: false,
  text: '',
  color: 'success'
})

// Load friends data
onMounted(async () => {
  loading.value = true
  await friendsStore.fetchFriends()
  loading.value = false
})

// Watch for changes in search query
watch(searchQuery, async (newQuery) => {
  // Debounce the search to avoid too many requests
  clearTimeout(searchTimeout.value)

  // Set a timeout to perform the search after a short delay
  searchTimeout.value = setTimeout(async () => {
    loading.value = true

    if (newQuery) {
      // If we have a search query, search with the birthday filter if needed
      if (showOnlyWithBirthday.value) {
        await searchFriendsWithBirthday(newQuery)
      } else {
        await friendsStore.searchFriends(newQuery)
      }
    } else {
      // If search is cleared, respect the birthday filter
      if (showOnlyWithBirthday.value) {
        await fetchFriendsWithBirthdays()
      } else {
        await friendsStore.fetchFriends()
      }
    }

    loading.value = false
  }, 300) // 300ms debounce
})

// Add a computed property to filter friends based on the toggle
const filteredFriendsList = computed(() => {
  if (!showOnlyWithBirthday.value) {
    return friendsStore.friends
  } else {
    return friendsStore.friends.filter(friend => friend.birthday)
  }
})

// Friends list - use our filtered friends when we're filtering by birthday
const friendsList = computed(() => {
  // When showing only friends with birthdays or when searching with birthday filter
  if (showOnlyWithBirthday.value) {
    return filteredFriends.value
  }
  // Otherwise use the store's friends list
  return friendsStore.friends
})

// Check if there are any friends
const hasFriends = computed(() => friendsStore.friends.length > 0)

// Handle adding a new friend
function handleAddFriend() {
  router.push({ name: 'addFriend' })
}

// Handle editing a friend
function handleEditFriend(friend) {
  router.push({ name: 'editFriend', params: { friendId: friend.id } })
}

// Handle deleting a friend
function handleDeleteFriend(friendId) {
  friendToDelete.value = friendId
  const friend = friendsStore.friends.find(f => f.id === friendId)
  if (friend) {
    showDeleteDialog.value = true
  }
}

// Handle viewing a friend's wishes
function handleViewWishes(friendId) {
  router.push({
    name: 'wishes',
    params: { friendId },
    query: { source: 'friends' } // Add source parameter to track where we came from
  })
}

// Note: uploadProfileImage function has been moved to the AddFriendView and EditFriendView components

// Note: handleSaveFriend and handleCancelDialog functions have been moved to their respective page components

// Handle confirming delete
async function handleConfirmDelete() {
  try {
    deleteLoading.value = true
    const friend = friendsStore.friends.find(f => f.id === friendToDelete.value)
    const friendName = friend ? friend.name : 'Friend'

    await friendsStore.deleteFriend(friendToDelete.value)

    snackbar.value = {
      show: true,
      text: `${friendName} has been removed from your friends.`,
      color: 'info'
    }
  } catch (error) {
    console.error('Error deleting friend:', error)
    snackbar.value = {
      show: true,
      text: 'Failed to delete friend. Please try again.',
      color: 'error'
    }
  } finally {
    deleteLoading.value = false
    showDeleteDialog.value = false
    friendToDelete.value = null
  }
}

// Handle canceling delete
function handleCancelDelete() {
  showDeleteDialog.value = false
  friendToDelete.value = null
}

// Handle importing contacts
function handleImportContacts() {
  router.push({ name: 'importContacts' })
}

// Clear search query and reload friends
async function clearSearch() {
  // Clear the search query
  searchQuery.value = ''

  // Cancel any pending search
  clearTimeout(searchTimeout.value)

  // Reload friends based on current filter state
  loading.value = true

  if (showOnlyWithBirthday.value) {
    await fetchFriendsWithBirthdays()
  } else {
    filteredFriends.value = [] // Clear filtered friends
    await friendsStore.fetchFriends()
  }

  loading.value = false
}

// Add a new function to reset all filters
function resetAllFilters() {
  searchQuery.value = ''
  showOnlyWithBirthday.value = false
  filteredFriends.value = [] // Clear filtered friends
  clearTimeout(searchTimeout.value)
  loading.value = true
  friendsStore.fetchFriends().then(() => {
    loading.value = false
  })
}

// Handle intersection for infinite scrolling
async function handleIntersect(isIntersecting) {
  console.log('Intersection detected:', isIntersecting)

  // If the element is visible and we're not already loading more friends
  if (isIntersecting && !friendsStore.loadingMore && friendsStore.hasMoreFriends) {
    console.log('Loading more friends...')
    // Load more friends
    await loadMoreFriends()
  }
}

// Function to load more friends (used by both intersection observer and button)
async function loadMoreFriends() {
  if (!friendsStore.loadingMore && friendsStore.hasMoreFriends) {
    await friendsStore.fetchMoreFriends()
  }
}

// Watch for changes in the birthday filter toggle
watch(showOnlyWithBirthday, async (newValue) => {
  loading.value = true

  if (newValue) {
    // If toggled on, fetch only friends with birthdays
    await fetchFriendsWithBirthdays()
  } else {
    // If toggled off, fetch all friends
    await friendsStore.fetchFriends()
  }

  loading.value = false
})

// Function to fetch only friends with birthdays
async function fetchFriendsWithBirthdays() {
  try {
    // Clear the search query when switching filters
    if (searchQuery.value) {
      searchQuery.value = ''
      clearTimeout(searchTimeout.value)
    }

    const { data, error } = await supabase
      .from('friends')
      .select('*')
      .not('birthday', 'is', null)
      .order('name')

    if (error) {
      console.error('Error fetching friends with birthdays:', error)
      throw error
    }

    // Create a local array to store the transformed friends
    let transformedFriends = []

    if (data) {
      // Convert snake_case to camelCase for UI
      transformedFriends = data.map(friend => ({
        id: friend.id,
        userId: friend.user_id,
        name: friend.name,
        email: friend.email,
        phoneNumber: friend.phone_number,
        birthday: friend.birthday,
        address: friend.address,
        notes: friend.notes,
        profileImage: friend.profile_image
      }))
    }

    // Update the filteredFriends ref instead of the computed property
    filteredFriends.value = transformedFriends

    // When directly querying, we don't want pagination
    friendsStore.hasMoreFriends = false

    return transformedFriends
  } catch (e) {
    console.error('Error fetching friends with birthdays:', e)
    return []
  }
}

// Function to search friends with birthdays
async function searchFriendsWithBirthday(query) {
  try {
    // Format the query for Supabase search
    const formattedQuery = `%${query.toLowerCase()}%`

    const { data, error } = await supabase
      .from('friends')
      .select('*')
      .not('birthday', 'is', null)
      .or(`name.ilike.${formattedQuery},email.ilike.${formattedQuery},phone_number.ilike.${formattedQuery}`)
      .order('name')

    if (error) {
      console.error('Error searching friends with birthdays:', error)
      throw error
    }

    // Create a local array to store the transformed friends
    let transformedFriends = []

    if (data) {
      // Convert snake_case to camelCase for UI
      transformedFriends = data.map(friend => ({
        id: friend.id,
        userId: friend.user_id,
        name: friend.name,
        email: friend.email,
        phoneNumber: friend.phone_number,
        birthday: friend.birthday,
        address: friend.address,
        notes: friend.notes,
        profileImage: friend.profile_image
      }))
    }

    // Update the filtered friends ref
    filteredFriends.value = transformedFriends

    // When searching, we don't want pagination
    friendsStore.hasMoreFriends = false

    return transformedFriends
  } catch (e) {
    console.error('Error searching friends with birthdays:', e)
    return []
  }
}
</script>

<template>
  <div class="friends-view">
    <v-container class="pt-0">
      <h1 class="text-h4 mb-2 font-weight-bold">Friends</h1>
      <p class="text-body-1 mb-3">
        Manage your friends and their information.
      </p>

      <!-- Search and Action Bar -->
      <v-row class="mb-4">
        <v-col cols="12" sm="6" md="8">
          <v-text-field
            v-model="searchQuery"
            label="Search friends"
            variant="outlined"
            density="compact"
            prepend-inner-icon="mdi-magnify"
            clearable
            @click:clear="clearSearch"
          ></v-text-field>
        </v-col>

        <v-col cols="12" sm="6" md="4" class="d-flex justify-end align-center">
          <v-btn
            color="primary"
            prepend-icon="mdi-account-plus"
            class="mr-2"
            @click="handleAddFriend"
          >
            Add Friend
          </v-btn>

          <v-btn
            variant="outlined"
            prepend-icon="mdi-import"
            @click="handleImportContacts"
          >
            Import
          </v-btn>
        </v-col>
      </v-row>

      <!-- Add Birthday Filter Toggle -->
      <v-row class="mb-4">
        <v-col cols="12">
          <v-switch
            v-model="showOnlyWithBirthday"
            label="Show only friends with birthdays"
            color="primary"
            hide-details
          ></v-switch>
        </v-col>
      </v-row>

      <!-- Loading Progress -->
      <v-progress-linear
        v-if="loading"
        indeterminate
        color="primary"
      ></v-progress-linear>

      <!-- No Friends Message -->
      <v-alert
        v-if="!loading && !hasFriends"
        type="info"
        variant="tonal"
        class="mb-4"
      >
        <p>You haven't added any friends yet.</p>
        <v-btn
          color="primary"
          variant="text"
          @click="handleAddFriend"
          class="mt-2"
        >
          Add Your First Friend
        </v-btn>
      </v-alert>

      <!-- No Friends With Birthday Message -->
      <v-alert
        v-else-if="!loading && showOnlyWithBirthday && friendsList.length === 0 && hasFriends"
        type="info"
        variant="tonal"
        class="mb-4"
      >
        <p>None of your friends have birthdays set.</p>
        <v-btn
          color="primary"
          variant="text"
          @click="showOnlyWithBirthday = false"
          class="mt-2 mr-2"
        >
          Show All Friends
        </v-btn>
      </v-alert>

      <!-- No Search Results -->
      <v-alert
        v-else-if="!loading && searchQuery && friendsList.length === 0"
        type="info"
        variant="tonal"
        class="mb-4"
      >
        <p>No friends match your search for "{{ searchQuery }}"{{ showOnlyWithBirthday ? ' with birthdays' : '' }}.</p>
        <div class="mt-2">
          <v-btn
            color="primary"
            variant="text"
            @click="clearSearch"
            class="mr-2"
          >
            Clear Search
          </v-btn>
          <v-btn
            v-if="showOnlyWithBirthday"
            color="primary"
            variant="text"
            @click="showOnlyWithBirthday = false"
          >
            Show All Friends
          </v-btn>
        </div>
      </v-alert>

      <!-- Friends List -->
      <div v-if="!loading && friendsList.length > 0">
        <div v-for="friend in friendsList" :key="friend.id">
          <FriendCard
            :friend="friend"
            @edit="handleEditFriend"
            @delete="handleDeleteFriend"
            @view-wishes="handleViewWishes"
          />
        </div>

        <!-- Intersection observer for infinite scrolling - only show when not searching -->
        <div
          v-if="!searchQuery && friendsStore.hasMoreFriends"
          v-intersect="{
            handler: handleIntersect,
            options: {
              threshold: 0.1,
              rootMargin: '100px'
            }
          }"
          class="text-center py-6 mt-4"
          style="min-height: 100px;"
        >
          <v-progress-circular
            v-if="friendsStore.loadingMore"
            indeterminate
            color="primary"
            size="24"
            class="mb-2"
          ></v-progress-circular>

          <div v-else>
            <div class="text-caption text-medium-emphasis mb-2">Scroll for more</div>
            <v-btn
              variant="outlined"
              size="small"
              color="primary"
              @click="loadMoreFriends"
              :disabled="friendsStore.loadingMore"
            >
              Load More Friends
            </v-btn>
          </div>
        </div>

        <!-- Message when all friends are loaded -->
        <div
          v-if="!searchQuery && !friendsStore.hasMoreFriends && friendsList.length > 0"
          class="text-center py-4 mt-2"
        >
          <span class="text-caption text-medium-emphasis">All friends loaded</span>
        </div>
      </div>

      <!-- Dialogs replaced with dedicated pages -->

      <!-- Delete Confirmation Dialog -->
      <DeleteConfirmationDialog
        v-model="showDeleteDialog"
        :loading="deleteLoading"
        title="Delete Friend"
        message="Are you sure you want to delete this friend? This action cannot be undone and will remove all associated wishes."
        @confirm="handleConfirmDelete"
        @cancel="handleCancelDelete"
      />

      <!-- Snackbar for notifications -->
      <v-snackbar
        v-model="snackbar.show"
        :color="snackbar.color"
        timeout="3000"
      >
        {{ snackbar.text }}
        <template v-slot:actions>
          <v-btn
            variant="text"
            @click="snackbar.show = false"
          >
            Close
          </v-btn>
        </template>
      </v-snackbar>
    </v-container>
  </div>
</template>

<style scoped>
.friends-view {
  padding-bottom: 80px; /* Add padding for bottom navigation */
}
</style>
