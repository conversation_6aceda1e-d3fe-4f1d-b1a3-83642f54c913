<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useNotifications } from '@/composables/useNotifications'
import { useAuth } from '@/composables/useAuth'
import { NotificationService } from '@/services/NotificationService'
import { NotificationTester } from '@/utils/notificationTester'
import { Capacitor } from '@capacitor/core'
import { supabase } from '@/lib/supabase'

const router = useRouter()
const notificationsStore = useNotifications()
const authStore = useAuth()

// Notification preferences
const notificationPreferences = ref({
  enabled: false,
  birthdayReminders: true,
  birthdayReminderDays: [1, 7],
  wishUpdates: true,
  friendActivity: true
})

// UI state
const loading = ref(false)
const testResults = ref(null)
const showTestResults = ref(false)
const deviceTokens = ref([])
const loadingTokens = ref(false)

// Snackbar
const snackbar = ref({
  show: false,
  text: '',
  color: 'success'
})

// Platform info
const platform = computed(() => Capacitor.getPlatform())
const isPushAvailable = computed(() => NotificationService.isPushAvailable())
const isWebNotificationAvailable = computed(() => NotificationService.isWebNotificationAvailable())

// Load notification preferences
onMounted(async () => {
  if (!authStore.user) return

  loading.value = true

  try {
    // Get notification preferences from Supabase
    const { data, error } = await supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', authStore.user.id)
      .single()

    if (error && error.code !== 'PGRST116') { // Not found error
      console.error('Error loading notification preferences:', error)
    }

    if (data) {
      notificationPreferences.value = {
        enabled: data.enabled,
        birthdayReminders: data.birthday_reminders,
        birthdayReminderDays: data.birthday_reminder_days || [1, 7],
        wishUpdates: data.wish_updates,
        friendActivity: data.friend_activity
      }
    } else {
      // Use values from local storage as fallback
      notificationPreferences.value.enabled = notificationsStore.isEnabled
    }

    // Load device tokens
    await loadDeviceTokens()
  } catch (error) {
    console.error('Error in onMounted:', error)
  } finally {
    loading.value = false
  }
})

// Load device tokens
async function loadDeviceTokens() {
  if (!authStore.user) return

  loadingTokens.value = true

  try {
    const { data, error } = await supabase
      .from('device_tokens')
      .select('*')
      .eq('user_id', authStore.user.id)

    if (error) {
      console.error('Error loading device tokens:', error)
    } else {
      deviceTokens.value = data || []
    }
  } catch (error) {
    console.error('Error loading device tokens:', error)
  } finally {
    loadingTokens.value = false
  }
}

// Save notification preferences
async function savePreferences() {
  if (!authStore.user) return

  loading.value = true

  try {
    // Update in Supabase
    const { error } = await supabase
      .from('notification_preferences')
      .upsert({
        user_id: authStore.user.id,
        enabled: notificationPreferences.value.enabled,
        birthday_reminders: notificationPreferences.value.birthdayReminders,
        birthday_reminder_days: notificationPreferences.value.birthdayReminderDays,
        wish_updates: notificationPreferences.value.wishUpdates,
        friend_activity: notificationPreferences.value.friendActivity
      }, { onConflict: 'user_id' })

    if (error) {
      console.error('Error saving notification preferences:', error)

      // Show error message
      snackbar.value = {
        show: true,
        text: 'Failed to save notification preferences',
        color: 'error'
      }
    } else {
      // Update local state
      await notificationsStore.toggleNotifications(
        notificationPreferences.value.enabled,
        authStore.user.id
      )

      // Show success message
      snackbar.value = {
        show: true,
        text: 'Notification preferences saved successfully',
        color: 'success'
      }

      // Navigate back to profile page after a short delay
      setTimeout(() => {
        router.push({ name: 'profile' })
      }, 1000)
    }
  } catch (error) {
    console.error('Error saving preferences:', error)
  } finally {
    loading.value = false
  }
}

// Cancel and go back to profile
function cancelAndGoBack() {
  router.push({ name: 'profile' })
}

// Test notifications
async function testNotification() {
  if (!authStore.user) return

  loading.value = true
  showTestResults.value = true

  try {
    testResults.value = await NotificationTester.runAllTests(authStore.user.id)
  } catch (error) {
    console.error('Error testing notification:', error)
    testResults.value = { error: error.message }
  } finally {
    loading.value = false
  }
}

// Delete device token
async function deleteToken(tokenId) {
  if (!authStore.user) return

  try {
    const { error } = await supabase
      .from('device_tokens')
      .delete()
      .eq('id', tokenId)
      .eq('user_id', authStore.user.id)

    if (error) {
      console.error('Error deleting device token:', error)
    } else {
      // Refresh device tokens
      await loadDeviceTokens()
    }
  } catch (error) {
    console.error('Error deleting token:', error)
  }
}

// Format date
function formatDate(dateString) {
  const date = new Date(dateString)
  return date.toLocaleString()
}
</script>

<template>
  <div class="notification-settings">
    <v-card class="mx-auto" max-width="600">
      <v-card-title class="text-h5">
        Notification Settings
      </v-card-title>

      <v-card-text>
        <v-alert v-if="!isPushAvailable && !isWebNotificationAvailable" type="warning" class="mb-4">
          Notifications are not supported on this device or browser.
        </v-alert>

        <v-alert v-else-if="isPushAvailable" type="info" class="mb-4">
          Push notifications are available on this {{ platform }} device.
        </v-alert>

        <v-alert v-else-if="isWebNotificationAvailable" type="info" class="mb-4">
          Web notifications are available in this browser.
        </v-alert>

        <v-form @submit.prevent="savePreferences">
          <v-switch
            v-model="notificationPreferences.enabled"
            color="primary"
            label="Enable notifications"
            hide-details
            class="mb-4"
          ></v-switch>

          <v-divider class="mb-4"></v-divider>

          <div class="text-subtitle-1 mb-2">Notification Types</div>

          <v-switch
            v-model="notificationPreferences.birthdayReminders"
            color="primary"
            label="Birthday reminders"
            :disabled="!notificationPreferences.enabled"
            hide-details
            class="mb-2"
          ></v-switch>

          <v-select
            v-model="notificationPreferences.birthdayReminderDays"
            :items="[
              { title: '1 day before', value: 1 },
              { title: '3 days before', value: 3 },
              { title: '7 days before', value: 7 },
              { title: '14 days before', value: 14 }
            ]"
            label="When to send birthday reminders"
            :disabled="!notificationPreferences.enabled || !notificationPreferences.birthdayReminders"
            multiple
            chips
            class="mb-4"
          ></v-select>

          <v-switch
            v-model="notificationPreferences.wishUpdates"
            color="primary"
            label="Wish updates"
            :disabled="!notificationPreferences.enabled"
            hide-details
            class="mb-2"
          ></v-switch>

          <v-switch
            v-model="notificationPreferences.friendActivity"
            color="primary"
            label="Friend activity"
            :disabled="!notificationPreferences.enabled"
            hide-details
            class="mb-4"
          ></v-switch>

          <v-divider class="mb-4"></v-divider>

          <v-btn
            color="primary"
            type="submit"
            :loading="loading"
            class="mr-2"
          >
            Save Settings
          </v-btn>

          <v-btn
            color="error"
            variant="outlined"
            @click="cancelAndGoBack"
            :disabled="loading"
            class="mr-2"
          >
            Cancel
          </v-btn>

          <v-btn
            variant="outlined"
            @click="testNotification"
            :loading="loading"
            :disabled="!notificationPreferences.enabled"
          >
            Test Notification
          </v-btn>
        </v-form>

        <v-expand-transition>
          <div v-if="showTestResults && testResults">
            <v-divider class="my-4"></v-divider>
            <div class="text-subtitle-1 mb-2">Test Results</div>

            <v-alert
              :type="testResults.error ? 'error' : (testResults.localNotification?.success ? 'success' : 'warning')"
              class="mb-2"
            >
              <template v-if="testResults.error">
                {{ testResults.error }}
              </template>
              <template v-else>
                Local notification: {{ testResults.localNotification?.success ? 'Success' : 'Failed' }}
                <div v-if="testResults.localNotification?.error" class="text-caption">
                  {{ testResults.localNotification.error }}
                </div>
              </template>
            </v-alert>

            <v-alert
              v-if="testResults.pushNotification"
              :type="testResults.pushNotification.success ? 'success' : 'error'"
              class="mb-2"
            >
              Push notification: {{ testResults.pushNotification.success ? 'Success' : 'Failed' }}
              <div v-if="testResults.pushNotification.error" class="text-caption">
                {{ testResults.pushNotification.error }}
              </div>
            </v-alert>
          </div>
        </v-expand-transition>

        <v-divider class="my-4"></v-divider>

        <div class="text-subtitle-1 mb-2">Registered Devices</div>
        <v-progress-linear v-if="loadingTokens" indeterminate></v-progress-linear>

        <v-list v-if="deviceTokens.length > 0">
          <v-list-item
            v-for="token in deviceTokens"
            :key="token.id"
          >
            <template v-slot:prepend>
              <v-icon :icon="token.platform === 'ios' ? 'mdi-apple' : (token.platform === 'android' ? 'mdi-android' : 'mdi-web')"></v-icon>
            </template>

            <v-list-item-title>{{ token.platform }}</v-list-item-title>
            <v-list-item-subtitle>Registered: {{ formatDate(token.created_at) }}</v-list-item-subtitle>

            <template v-slot:append>
              <v-btn
                icon="mdi-delete"
                variant="text"
                density="comfortable"
                @click="deleteToken(token.id)"
              ></v-btn>
            </template>
          </v-list-item>
        </v-list>

        <v-alert v-else-if="!loadingTokens" type="info" class="mt-2">
          No devices registered for notifications.
        </v-alert>
      </v-card-text>
    </v-card>

    <!-- Snackbar for notifications -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="3000"
    >
      {{ snackbar.text }}
      <template v-slot:actions>
        <v-btn
          variant="text"
          @click="snackbar.show = false"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<style scoped>
.notification-settings {
  padding: 16px;
}
</style>
