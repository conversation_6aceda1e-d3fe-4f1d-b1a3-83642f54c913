<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '@/composables/useAuth'
import { supabase } from '@/lib/supabase'

const router = useRouter()
const authStore = useAuth()
const loading = ref(false)
const valid = ref(true)
const form = ref(null)

// Add snackbar ref
const snackbar = ref({
  show: false,
  text: '',
  color: 'error'
})

const personalInfo = ref({
  name: '',
  birthday: '',
  profileImage: null,
  imageUrl: null
})

// Save form state to localStorage whenever it changes
watch(personalInfo, (newValue) => {
  const stateToSave = {
    name: newValue.name,
    birthday: newValue.birthday,
    imageUrl: newValue.imageUrl
  }
  localStorage.setItem('personalInfoState', JSON.stringify(stateToSave))
}, { deep: true })

// Restore state on mount
onMounted(() => {
  const savedState = localStorage.getItem('personalInfoState')
  if (savedState) {
    const parsedState = JSON.parse(savedState)
    personalInfo.value.name = parsedState.name
    personalInfo.value.birthday = parsedState.birthday
    personalInfo.value.imageUrl = parsedState.imageUrl
  }
})

// Form validation rules
const rules = {
  name: [
    v => !!v || 'Name is required',
    v => v.length <= 50 || 'Name must be less than 50 characters'
  ],
  birthday: [
    v => !!v || 'Birthday is required',
    v => {
      const date = new Date(v)
      return date <= new Date() || 'Birthday cannot be in the future'
    }
  ]
}

async function handleImageUpload(event) {
  const file = event.target.files[0]
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      personalInfo.value.imageUrl = e.target.result
    }
    reader.readAsDataURL(file)
    personalInfo.value.profileImage = file
  }
}

async function uploadProfileImage(userId) {
  if (!personalInfo.value.profileImage) return null

  try {
    const fileExt = personalInfo.value.profileImage.name.split('.').pop()
    const fileName = `${userId}/profile.${fileExt}`

    const { error: uploadError, data } = await supabase.storage
      .from('profile-images')  // Changed from 'profiles' to 'profile-images'
      .upload(fileName, personalInfo.value.profileImage, {
        upsert: true
      })

    if (uploadError) throw uploadError

    // Get the public URL for the uploaded image
    const { data: { publicUrl } } = supabase.storage
      .from('profile-images')  // Changed from 'profiles' to 'profile-images'
      .getPublicUrl(data.path)

    return publicUrl
  } catch (error) {
    console.error('Upload error:', error)
    throw new Error('Failed to upload profile image')
  }
}

async function handleSubmit() {
  const { valid } = await form.value.validate()
  
  if (!valid) return

  try {
    loading.value = true

    if (!authStore.user) {
      throw new Error('No authenticated user found')
    }

    let profileImageUrl = null
    if (personalInfo.value.profileImage) {
      profileImageUrl = await uploadProfileImage(authStore.user.id)
    }

    const { error: updateError } = await supabase
      .from('profiles')
      .upsert({
        id: authStore.user.id,
        name: personalInfo.value.name,
        birthday: personalInfo.value.birthday,
        profile_image: profileImageUrl,
        onboarding_complete: false // Keep false until contacts import step is complete
      })

    if (updateError) throw updateError

    // Clear saved state after successful submission
    localStorage.removeItem('personalInfoState')
    
    await authStore.fetchProfile()
    router.push({ name: 'importContacts' })
  } catch (e) {
    console.error('Submit error:', e)
    snackbar.value = {
      show: true,
      text: e.message || 'Failed to update profile',
      color: 'error'
    }
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <v-container class="fill-height" fluid>
    <v-row align="center" justify="center">
      <v-col cols="12" sm="8" md="6" lg="4">
        <v-card class="elevation-12">
          <v-toolbar color="primary" dark flat>
            <v-toolbar-title>Complete Your Profile</v-toolbar-title>
          </v-toolbar>

          <v-card-text>
            <v-form
              ref="form"
              v-model="valid"
              @submit.prevent="handleSubmit"
            >
              <!-- Profile Image Upload -->
              <div class="text-center mb-4">
                <v-avatar size="150" class="mb-2">
                  <v-img
                    v-if="personalInfo.imageUrl"
                    :src="personalInfo.imageUrl"
                    alt="Profile preview"
                  />
                  <v-icon
                    v-else
                    size="large"
                  >
                    mdi-account-circle
                  </v-icon>
                </v-avatar>
                
                <v-file-input
                  v-model="personalInfo.profileImage"
                  accept="image/*"
                  prepend-icon="mdi-camera"
                  label="Profile Picture"
                  hide-details
                  class="mt-2"
                  @change="handleImageUpload"
                />
              </div>

              <v-text-field
                v-model="personalInfo.name"
                :rules="rules.name"
                label="Name"
                required
                prepend-inner-icon="mdi-account"
                variant="outlined"
                :disabled="loading"
              />

              <v-text-field
                v-model="personalInfo.birthday"
                :rules="rules.birthday"
                label="Birthday"
                type="date"
                required
                prepend-inner-icon="mdi-cake"
                variant="outlined"
                :disabled="loading"
              />

              <v-btn
                :loading="loading"
                block
                color="primary"
                size="large"
                type="submit"
                :disabled="!valid"
              >
                Complete Profile
              </v-btn>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Error Snackbar -->
    <v-snackbar
      v-model="snackbar.show"
      :color="snackbar.color"
      timeout="5000"
    >
      {{ snackbar.text }}
      
      <template v-slot:actions>
        <v-btn
          color="white"
          variant="text"
          @click="snackbar.show = false"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>
