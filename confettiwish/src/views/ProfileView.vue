<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '@/composables/useAuth'
import { useLocalization } from '@/composables/useLocalization'
import { useTheme } from '@/composables/useTheme'
import { useNotifications } from '@/composables/useNotifications'
import { storeToRefs } from 'pinia'
import { supabase } from '@/lib/supabase'
import NotificationPermission from '@/components/NotificationPermission.vue'

const router = useRouter()
const authStore = useAuth()
const localizationStore = useLocalization()
const themeStore = useTheme()
const notificationsStore = useNotifications()
const loading = ref(false)
const userProfile = ref(null)
const notificationsEnabled = ref(false) // Local ref for notifications state
const notifications = computed({
  get: () => notificationsEnabled.value,
  set: (value) => toggleNotifications(value)
})
const darkMode = computed({
  get: () => themeStore.isDark,
  set: (value) => themeStore.setDarkMode(value)
})
const selectedLocale = ref('')
const selectedCurrency = ref('')
// Flag to track if we're in the initialization phase
const isInitializing = ref(true)

// Snackbar for notifications
const snackbar = ref({
  show: false,
  text: '',
  color: 'success'
})

// Get the profile image URL
const profileImageUrl = ref(null)

// Load user profile data
onMounted(async () => {
  loading.value = true
  isInitializing.value = true // Set initializing flag

  await authStore.fetchProfile()
  userProfile.value = authStore.profile

  // Generate signed URL for profile image if it exists
  if (userProfile.value?.profile_image) {
    await generateProfileImageUrl()
  }

  // Load locale and default currency from user profile
  if (authStore.user) {
    // Only load locale if it's not already loaded
    if (!localizationStore.isLoaded) {
      await localizationStore.loadLocale(authStore.user.id)
    }
    // Set the selected values from the store
    selectedLocale.value = localizationStore.locale
    selectedCurrency.value = localizationStore.defaultCurrency || ''

    // Initialize notifications
    await notificationsStore.initialize(authStore.user.id)
    
    // Set the local notifications state from the store
    notificationsEnabled.value = notificationsStore.isEnabled
  }

  loading.value = false

  // Use nextTick to ensure all reactive updates have been processed
  // before we stop initializing
  nextTick(() => {
    isInitializing.value = false
    console.log('Initialization complete')
  })
})

// Watch for changes to notifications state in the store
watch(() => notificationsStore.isEnabled, (newValue) => {
  notificationsEnabled.value = newValue
})

// Make sure the settings CTA button is enabled when notification settings change
watch(notificationsEnabled, (newValue) => {
  console.log('Local notifications enabled state changed:', newValue)
  // This watch function ensures the UI properly updates when the notifications state changes
  // The computed property will be recalculated when notificationsEnabled.value changes
})

// Watch for changes to selectedLocale and update the user's profile
watch(selectedLocale, async (newLocale, oldLocale) => {
  // Skip updates during initialization
  if (isInitializing.value) {
    console.log('Skipping locale update during initialization');
    return;
  }

  // Only update if the locale has actually changed and is different from the store value
  if (newLocale && authStore.user && newLocale !== oldLocale && newLocale !== localizationStore.locale) {
    console.log(`Updating locale from ${oldLocale} to ${newLocale}`);
    await localizationStore.updateLocale(authStore.user.id, newLocale)

    // If no custom currency is set, update the currency to match the locale
    if (!selectedCurrency.value || selectedCurrency.value === '') {
      // Get the default currency for this locale
      const localeCurrency = localizationStore.getDefaultCurrencyForLocale(newLocale)

      // Update the currency in the UI and database
      selectedCurrency.value = localeCurrency
      await localizationStore.updateDefaultCurrency(authStore.user.id, localeCurrency)
    }

    snackbar.value = {
      show: true,
      text: 'Locale updated successfully!',
      color: 'success'
    }
  }
})

// Watch for changes to selectedCurrency and update the user's profile
watch(selectedCurrency, async (newCurrency, oldCurrency) => {
  // Skip updates during initialization
  if (isInitializing.value) {
    console.log('Skipping currency update during initialization');
    return;
  }

  // Only update if the currency has actually changed and is different from the store value
  if (authStore.user && newCurrency !== oldCurrency && newCurrency !== localizationStore.defaultCurrency) {
    console.log(`Updating currency from ${oldCurrency} to ${newCurrency}`);
    // If empty string, set to null to use locale-based currency
    const currencyToSave = newCurrency || null

    await localizationStore.updateDefaultCurrency(authStore.user.id, currencyToSave)

    snackbar.value = {
      show: true,
      text: 'Default currency updated successfully!',
      color: 'success'
    }
  }
})

// Generate a signed URL for the profile image
async function generateProfileImageUrl() {
  if (!userProfile.value?.profile_image) return

  try {
    const { data, error } = await supabase.storage
      .from('profile-images')
      .createSignedUrl(userProfile.value.profile_image, 60 * 60) // 1 hour expiry

    if (error) {
      console.error('Error creating signed URL:', error)
      // If the image doesn't exist, set profileImageUrl to null to show default avatar
      profileImageUrl.value = null

      // Update the profile to remove the invalid image reference
      if (error.message.includes('Object not found') && authStore.user) {
        try {
          // Update the profile to remove the invalid image path
          await supabase
            .from('profiles')
            .update({ profile_image: null })
            .eq('id', authStore.user.id)

          // Update local profile data
          if (userProfile.value) {
            userProfile.value.profile_image = null
          }

          console.log('Removed invalid profile image reference from user profile')
        } catch (updateError) {
          console.error('Error updating profile to remove invalid image:', updateError)
        }
      }
      return
    }

    profileImageUrl.value = data.signedUrl
  } catch (error) {
    console.error('Error generating profile image URL:', error)
    // Set to null to show default avatar on error
    profileImageUrl.value = null
  }
}

// Handle image loading errors
async function handleImageError() {
  console.error('Profile image failed to load:', profileImageUrl.value)

  // Set to null to show default avatar
  profileImageUrl.value = null

  // If the profile has an image path, try to update the profile to remove it
  if (userProfile.value?.profile_image && authStore.user) {
    try {
      // Update the profile to remove the invalid image path
      await supabase
        .from('profiles')
        .update({ profile_image: null })
        .eq('id', authStore.user.id)

      // Update local profile data
      userProfile.value.profile_image = null

      console.log('Removed invalid profile image reference from user profile')
    } catch (updateError) {
      console.error('Error updating profile to remove invalid image:', updateError)
    }
  }
}

// Handle editing profile
function handleEditProfile() {
  router.push({ name: 'editProfile' })
}

// Note: handleSaveProfile and handleCancelEdit functions have been moved to the EditProfileView component

// Note: uploadProfileImage function has been moved to the EditProfileView component

// Handle sign out
async function handleSignOut() {
  try {
    loading.value = true
    await authStore.signOut()
    // Explicitly navigate to the login page
    router.push({ name: 'login' })
  } catch (error) {
    console.error('Error signing out:', error)
    snackbar.value = {
      show: true,
      text: 'Failed to sign out. Please try again.',
      color: 'error'
    }
  } finally {
    loading.value = false
  }
}

// Format date for display
function formatDate(dateString) {
  if (!dateString) return 'Not set'
  return localizationStore.formatDate(dateString)
}

// Toggle notifications
async function toggleNotifications(value) {
  console.log('toggleNotifications called with value:', value)
  if (authStore.user) {
    console.log('User ID:', authStore.user.id)

    // Show loading indicator
    loading.value = true

    try {
      // Call the store function and wait for the result
      const result = await notificationsStore.toggleNotifications(value, authStore.user.id)
      console.log('Toggle result:', result)
      
      // Directly update our local ref to reflect the new state immediately
      notificationsEnabled.value = value
      
      // Show success or error message based on the detailed result
      if (result.success) {
        snackbar.value = {
          show: true,
          text: value ? 'Notifications enabled' : 'Notifications disabled',
          color: 'success'
        }
      } else if (result.error) {
        // If permission was denied but we attempted to enable
        if (value && !result.permissionGranted) {
          snackbar.value = {
            show: true,
            text: 'Notification permission denied by browser. Please check your browser settings.',
            color: 'warning'
          }
          // Roll back the local state since permissions were denied
          notificationsEnabled.value = false
        } else {
          snackbar.value = {
            show: true,
            text: `Error: ${result.error}`,
            color: 'error'
          }
          // Roll back the local state to match the store
          notificationsEnabled.value = notificationsStore.isEnabled
        }
      }
    } catch (error) {
      console.error('Error toggling notifications:', error)
      snackbar.value = {
        show: true,
        text: 'Error toggling notifications',
        color: 'error'
      }
    } finally {
      loading.value = false
    }
  } else {
    console.error('No user found when toggling notifications')
  }
}
</script>

<template>
  <div class="profile-view">
    <v-container class="pt-0">
      <h1 class="text-h4 mb-2 font-weight-bold">Profile</h1>

      <!-- Loading Progress -->
      <v-progress-linear
        v-if="loading"
        indeterminate
        color="primary"
        class="mb-4"
      ></v-progress-linear>

      <div v-else-if="userProfile">
        <!-- Profile Card -->
        <v-card class="mb-6">
          <v-card-item>
            <template v-slot:prepend>
              <v-avatar size="120" class="mr-4">
                <v-img
                  v-if="profileImageUrl"
                  :src="profileImageUrl"
                  :alt="userProfile.name || 'Profile'"
                  @error="handleImageError"
                ></v-img>
                <v-icon v-else icon="mdi-account" size="80"></v-icon>
              </v-avatar>
            </template>

            <v-card-title class="text-h5">
              {{ userProfile.name || 'Your Name' }}
            </v-card-title>

            <v-card-subtitle>
              {{ userProfile.email }}
            </v-card-subtitle>
          </v-card-item>

          <v-card-text>
            <v-list>
              <v-list-item>
                <template v-slot:prepend>
                  <v-icon icon="mdi-cake-variant" class="mr-2"></v-icon>
                </template>
                <v-list-item-title>Birthday</v-list-item-title>
                <v-list-item-subtitle>{{ formatDate(userProfile.birthday) }}</v-list-item-subtitle>
              </v-list-item>

              <v-list-item>
                <template v-slot:prepend>
                  <v-icon icon="mdi-phone" class="mr-2"></v-icon>
                </template>
                <v-list-item-title>Phone</v-list-item-title>
                <v-list-item-subtitle>{{ userProfile.phone_number || 'Not set' }}</v-list-item-subtitle>
              </v-list-item>

              <v-list-item>
                <template v-slot:prepend>
                  <v-icon icon="mdi-map-marker" class="mr-2"></v-icon>
                </template>
                <v-list-item-title>Address</v-list-item-title>
                <v-list-item-subtitle>{{ userProfile.address || 'Not set' }}</v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-card-text>

          <v-card-actions>
            <v-btn
              color="primary"
              prepend-icon="mdi-account-edit"
              @click="handleEditProfile"
            >
              Edit Profile
            </v-btn>
            <v-spacer></v-spacer>
            <v-btn
              color="error"
              variant="text"
              prepend-icon="mdi-logout"
              @click="handleSignOut"
              :loading="loading"
            >
              Sign Out
            </v-btn>
          </v-card-actions>
        </v-card>

        <!-- Account Settings Section -->
        <v-card class="mt-4">
          <v-card-item>
            <v-card-title class="text-h6">Account Settings</v-card-title>
          </v-card-item>

          <v-card-text>
            <!-- Notification Permission Alert -->
            <NotificationPermission />
            <!-- Notifications Setting -->
            <div class="setting-item">
              <div class="setting-header">
                <v-icon icon="mdi-bell" color="primary" class="mr-2"></v-icon>
                <div class="setting-title">Notifications</div>
              </div>
              <div class="d-flex align-center">
                <v-switch
                  v-model="notifications"
                  color="primary"
                  hide-details
                  inset
                  class="mr-2"
                  :disabled="loading"
                ></v-switch>
                <v-btn
                  variant="text"
                  density="comfortable"
                  icon="mdi-cog"
                  size="small"
                  :to="{ name: 'notificationSettings' }"
                  :disabled="!notifications"
                  title="Notification Settings"
                ></v-btn>
              </div>
            </div>

            <!-- Dark Mode Setting -->
            <div class="setting-item">
              <div class="setting-header">
                <v-icon icon="mdi-theme-light-dark" color="primary" class="mr-2"></v-icon>
                <div class="setting-title">Dark Mode</div>
              </div>
              <v-switch
                v-model="darkMode"
                color="primary"
                hide-details
                inset
              ></v-switch>
            </div>

            <!-- Locale Setting -->
            <div class="setting-item">
              <div class="setting-header">
                <v-icon icon="mdi-translate" color="primary" class="mr-2"></v-icon>
                <div class="setting-title">Locale</div>
              </div>
              <v-select
                v-model="selectedLocale"
                :items="localizationStore.availableLocales"
                item-title="name"
                item-value="code"
                variant="outlined"
                density="compact"
                hide-details
                class="setting-control"
              ></v-select>
            </div>

            <!-- Default Currency Setting -->
            <div class="setting-item">
              <div class="setting-header">
                <v-icon icon="mdi-currency-usd" color="primary" class="mr-2"></v-icon>
                <div class="setting-title">Default Currency</div>
              </div>
              <v-select
                v-model="selectedCurrency"
                :items="[{ code: '', name: 'Use locale default' }, ...localizationStore.availableCurrencies]"
                item-title="name"
                item-value="code"
                variant="outlined"
                density="compact"
                hide-details
                class="setting-control"
              >
                <template v-slot:selection="{ item }">
                  <span v-if="item.value">{{ item.value }}</span>
                  <span v-else>Default</span>
                </template>
                <template v-slot:item="{ item, props }">
                  <v-list-item v-bind="props">
                    <template v-slot:prepend v-if="item.raw.code">
                      <span class="font-weight-bold mr-2">{{ item.raw.symbol }}</span>
                    </template>
                    <v-list-item-title>
                      <span v-if="item.raw.code">{{ item.raw.code }} - {{ item.title }}</span>
                      <span v-else>{{ item.title }}</span>
                    </v-list-item-title>
                  </v-list-item>
                </template>
              </v-select>
            </div>

            <!-- Currency Format Preview -->
            <div class="setting-item">
              <div class="setting-header">
                <v-icon icon="mdi-cash" color="primary" class="mr-2"></v-icon>
                <div class="setting-title">Currency Format Preview</div>
              </div>
              <v-chip color="primary" size="small" class="setting-value">
                {{ localizationStore.formatCurrency(100, selectedCurrency) }}
              </v-chip>
            </div>
          </v-card-text>
        </v-card>
      </div>

      <v-alert
        v-else
        type="warning"
        variant="tonal"
        class="mt-4"
      >
        Failed to load profile information. Please try refreshing the page.
      </v-alert>

      <!-- Edit Profile Dialog replaced with a dedicated page -->

      <!-- Snackbar for notifications -->
      <v-snackbar
        v-model="snackbar.show"
        :color="snackbar.color"
        timeout="3000"
      >
        {{ snackbar.text }}
        <template v-slot:actions>
          <v-btn
            variant="text"
            @click="snackbar.show = false"
          >
            Close
          </v-btn>
        </template>
      </v-snackbar>
    </v-container>
  </div>
</template>

<style scoped>
.profile-view {
  padding-bottom: 80px; /* Add padding for bottom navigation */
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.setting-header {
  display: flex;
  align-items: center;
}

.setting-title {
  font-size: 1rem;
  font-weight: 500;
}

.setting-control {
  max-width: 200px;
  min-width: 120px;
}

.setting-value {
  min-width: 80px;
  text-align: center;
}

/* Responsive styles for mobile */
@media (max-width: 600px) {
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 16px 0;
  }

  .setting-header {
    margin-bottom: 8px;
    width: 100%;
  }

  .setting-control {
    max-width: 100%;
    width: 100%;
  }

  .setting-value {
    margin-top: 8px;
  }

  /* Special case for switches to keep them on the same line */
  .setting-item:has(> .v-switch) {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

/* Dark mode support */
:deep(.v-theme--dark) .setting-item {
  border-bottom-color: rgba(255, 255, 255, 0.12);
}
</style>
