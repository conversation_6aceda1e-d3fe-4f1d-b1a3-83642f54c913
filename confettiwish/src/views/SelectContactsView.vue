<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '@/composables/useAuth'
import { useFriends } from '@/composables/useFriends'
import { supabase } from '@/lib/supabase'

const router = useRouter()
const authStore = useAuth()
const friendsStore = useFriends()
const loading = ref(false)
const contacts = ref([])
const selectedContacts = ref([])

// Snackbar for notifications
const snackbar = ref({
  show: false,
  text: '',
  color: 'error'
})

// Get contacts from localStorage on mount
onMounted(() => {
  const savedContacts = localStorage.getItem('importedContacts')
  if (savedContacts) {
    contacts.value = JSON.parse(savedContacts)
    // Initialize all contacts as selected by default
    selectedContacts.value = contacts.value.map((_, index) => index)
  } else {
    // If no contacts found, redirect back to import page
    router.push({ name: 'importContacts' })
  }
})

// Computed property to check if all contacts are selected
const allSelected = computed(() => {
  return selectedContacts.value.length === contacts.value.length
})

// Computed property to check if some contacts are selected
const someSelected = computed(() => {
  return selectedContacts.value.length > 0 && !allSelected.value
})

// Toggle selection of all contacts
function toggleSelectAll() {
  if (allSelected.value) {
    selectedContacts.value = []
  } else {
    selectedContacts.value = contacts.value.map((_, index) => index)
  }
}

// Toggle selection of a single contact
function toggleSelect(index) {
  const position = selectedContacts.value.indexOf(index)
  if (position === -1) {
    selectedContacts.value.push(index)
  } else {
    selectedContacts.value.splice(position, 1)
  }
}

// Import selected contacts as friends
async function importSelectedContacts() {
  if (selectedContacts.value.length === 0) {
    snackbar.value = {
      show: true,
      text: 'Please select at least one contact to import',
      color: 'warning'
    }
    return
  }

  try {
    loading.value = true

    // Get the current user ID
    const userId = authStore.user.id

    // Create an array of selected contacts to import
    const contactsToImport = selectedContacts.value.map(index => {
      const contact = contacts.value[index]
      return {
        name: contact.name,
        email: contact.email || '',
        phoneNumber: contact.phoneNumber || '',
        birthday: contact.birthday || null,
        userId: userId
      }
    })

    // Define batch size
    const BATCH_SIZE = 25
    let successCount = 0
    let errorCount = 0

    // Process contacts in batches
    for (let i = 0; i < contactsToImport.length; i += BATCH_SIZE) {
      const batch = contactsToImport.slice(i, i + BATCH_SIZE)

      try {
        // Update progress message
        snackbar.value = {
          show: true,
          text: `Importing contacts... (${Math.min(i + BATCH_SIZE, contactsToImport.length)}/${contactsToImport.length})`,
          color: 'info'
        }

        // Use the batch import function
        const newFriends = await friendsStore.addFriendsBatch(batch)
        successCount += newFriends.length
        errorCount += batch.length - newFriends.length

      } catch (batchError) {
        console.error('Batch import error:', batchError)
        errorCount += batch.length
      }
    }

    // Show success message
    snackbar.value = {
      show: true,
      text: `Successfully imported ${successCount} contacts${errorCount > 0 ? `, ${errorCount} failed` : ''}`,
      color: errorCount > 0 ? 'warning' : 'success'
    }

    // Clear imported contacts from localStorage
    localStorage.removeItem('importedContacts')

    // If this was part of onboarding, mark it as complete
    if (router.currentRoute.value.query.onboarding === 'true') {
      const { error } = await supabase
        .from('profiles')
        .update({ onboarding_complete: true })
        .eq('id', userId)

      if (error) throw error

      await authStore.fetchProfile()

      // Redirect to home page instead of friends page for onboarding completion
      setTimeout(() => {
        router.push({ name: 'home' })
        return // Exit early to prevent the friends page redirect below
      }, 1500)
    }

    // Redirect to friends page after a short delay if not in onboarding flow
    if (router.currentRoute.value.query.onboarding !== 'true') {
      setTimeout(() => {
        router.push({ name: 'friends' })
      }, 1500)
    }

  } catch (error) {
    console.error('Import error:', error)
    snackbar.value = {
      show: true,
      text: error.message || 'Failed to import contacts',
      color: 'error'
    }
  } finally {
    loading.value = false
  }
}

// Cancel import and go back
async function cancelImport() {
  localStorage.removeItem('importedContacts')

  // Check if this is part of onboarding
  if (router.currentRoute.value.query.onboarding === 'true') {
    try {
      loading.value = true

      // Mark onboarding as complete even if skipping
      const userId = authStore.user.id
      const { error } = await supabase
        .from('profiles')
        .update({ onboarding_complete: true })
        .eq('id', userId)

      if (error) throw error

      await authStore.fetchProfile()

      // Redirect to home page
      router.push({ name: 'home' })
    } catch (error) {
      console.error('Error skipping import:', error)
      snackbar.value = {
        show: true,
        text: error.message || 'Failed to skip contact import',
        color: 'error'
      }
    } finally {
      loading.value = false
    }
  } else {
    // If not onboarding, just go back
    router.go(-1)
  }
}
</script>

<template>
  <v-container>
    <v-row justify="center">
      <v-col cols="12" md="8" lg="6">
        <v-card>
          <v-toolbar color="primary" dark>
            <v-toolbar-title>Select Contacts to Import</v-toolbar-title>
          </v-toolbar>

          <v-card-text>
            <p class="text-body-1 mb-4">
              Select the contacts you want to import as friends.
            </p>

            <!-- Selection controls -->
            <div class="d-flex align-center mb-4">
              <v-checkbox
                v-model="allSelected"
                :indeterminate="someSelected"
                label="Select All"
                hide-details
                @click="toggleSelectAll"
              ></v-checkbox>

              <v-spacer></v-spacer>

              <div class="text-caption">
                {{ selectedContacts.length }} of {{ contacts.length }} selected
              </div>
            </div>

            <!-- Contact list with checkboxes -->
            <v-list v-if="contacts.length > 0">
              <v-list-item
                v-for="(contact, index) in contacts"
                :key="index"
                :value="contact"
              >
                <template v-slot:prepend>
                  <v-checkbox
                    :model-value="selectedContacts.includes(index)"
                    hide-details
                    @click="toggleSelect(index)"
                  ></v-checkbox>
                </template>

                <v-list-item-title>{{ contact.name }}</v-list-item-title>
                <v-list-item-subtitle>
                  <div v-if="contact.email">Email: {{ contact.email }}</div>
                  <div v-if="contact.phoneNumber">Phone: {{ contact.phoneNumber }}</div>
                  <div v-if="contact.birthday">Birthday: {{ contact.birthday }}</div>
                </v-list-item-subtitle>
              </v-list-item>
            </v-list>

            <v-alert
              v-else
              type="info"
              text="No contacts found. Please go back and import contacts first."
            ></v-alert>
          </v-card-text>

          <v-divider></v-divider>

          <v-card-actions>
            <v-btn
              color="grey"
              variant="text"
              @click="cancelImport"
              :disabled="loading"
            >
              Cancel
            </v-btn>

            <v-spacer></v-spacer>

            <v-btn
              color="primary"
              @click="importSelectedContacts"
              :loading="loading"
              :disabled="selectedContacts.length === 0"
            >
              Import Selected ({{ selectedContacts.length }})
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>

    <!-- Snackbar for notifications -->
    <v-snackbar v-model="snackbar.show" :color="snackbar.color" timeout="5000">
      {{ snackbar.text }}

      <template v-slot:actions>
        <v-btn color="white" variant="text" @click="snackbar.show = false">
          Close
        </v-btn>
      </template>
    </v-snackbar>
  </v-container>
</template>

<style scoped>
.v-list-item {
  min-height: 64px;
}
</style>
