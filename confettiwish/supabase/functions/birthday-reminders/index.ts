// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

serve(async (req) => {
  try {
    // Create a Supabase client with the service role key
    const supabaseAdmin = createClient(
      // Supabase API URL - env var exported by default.
      Deno.env.get('SUPABASE_URL') ?? '',
      // Supabase SERVICE ROLE KEY - env var exported by default.
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get the current date
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    
    // Get users with notification preferences
    const { data: users, error: userError } = await supabaseAdmin
      .from('notification_preferences')
      .select('user_id, birthday_reminder_days')
      .eq('enabled', true)
      .eq('birthday_reminders', true)

    if (userError) {
      console.error('Error fetching users:', userError)
      return new Response(
        JSON.stringify({ error: 'Failed to fetch users' }),
        { headers: { 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    const results = []

    // Process each user
    for (const user of users || []) {
      try {
        const reminderDays = user.birthday_reminder_days || [1, 7]
        
        // Get user's friends with upcoming birthdays
        for (const days of reminderDays) {
          const targetDate = new Date(today)
          targetDate.setDate(today.getDate() + days)
          
          // Format date for SQL query (MM-DD)
          const targetMonth = (targetDate.getMonth() + 1).toString().padStart(2, '0')
          const targetDay = targetDate.getDate().toString().padStart(2, '0')
          const datePattern = `${targetMonth}-${targetDay}`
          
          // Query for friends with birthdays on the target date
          const { data: friends, error: friendError } = await supabaseAdmin
            .from('friends')
            .select('id, name, birth_date')
            .eq('user_id', user.user_id)
            .ilike('birth_date', `%-${datePattern}`)
          
          if (friendError) {
            console.error(`Error fetching friends for user ${user.user_id}:`, friendError)
            continue
          }
          
          // Send notifications for each friend
          for (const friend of friends || []) {
            // Check if we've already sent a notification for this birthday
            const birthYear = new Date(friend.birth_date).getFullYear()
            const currentYear = now.getFullYear()
            
            // Create a unique key for this birthday notification
            const notificationKey = `birthday_${friend.id}_${currentYear}_${days}days`
            
            // Check if we've already sent this notification
            const { data: existingNotif, error: notifCheckError } = await supabaseAdmin
              .from('notifications')
              .select('id')
              .eq('user_id', user.user_id)
              .eq('type', 'birthday_reminder')
              .eq('data->notificationKey', notificationKey)
              .limit(1)
            
            if (notifCheckError) {
              console.error('Error checking existing notifications:', notifCheckError)
            }
            
            // Skip if notification already sent
            if (existingNotif && existingNotif.length > 0) {
              results.push({
                user: user.user_id,
                friend: friend.id,
                status: 'skipped',
                reason: 'already_sent'
              })
              continue
            }
            
            // Calculate age if birth year is provided
            let ageText = ''
            if (birthYear && birthYear > 1900) {
              const age = currentYear - birthYear
              ageText = ` (turning ${age})`
            }
            
            // Create notification message
            const title = days === 1 
              ? `Birthday Tomorrow!` 
              : `Birthday in ${days} days`
              
            const body = days === 1
              ? `${friend.name}${ageText} has a birthday tomorrow!`
              : `${friend.name}${ageText} has a birthday in ${days} days.`
            
            // Call the send-notifications function
            const sendResponse = await fetch(
              `${Deno.env.get('SUPABASE_URL')}/functions/v1/send-notifications`,
              {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`
                },
                body: JSON.stringify({
                  type: 'birthday_reminder',
                  userId: user.user_id,
                  data: {
                    title,
                    body,
                    data: {
                      url: `/friends/${friend.id}`,
                      friendId: friend.id,
                      days,
                      notificationKey
                    }
                  }
                })
              }
            )
            
            const sendResult = await sendResponse.json()
            results.push({
              user: user.user_id,
              friend: friend.id,
              days,
              status: sendResponse.ok ? 'sent' : 'error',
              result: sendResult
            })
          }
        }
      } catch (error) {
        console.error(`Error processing user ${user.user_id}:`, error)
        results.push({
          user: user.user_id,
          status: 'error',
          error: error.message
        })
      }
    }

    return new Response(
      JSON.stringify({ 
        message: 'Birthday reminders processed', 
        processed: results.length,
        results 
      }),
      { headers: { 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Unexpected error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { headers: { 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})

// To invoke:
// curl -i --location --request POST 'http://localhost:54321/functions/v1/birthday-reminders' \
//   --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
//   --header 'Content-Type: application/json' \
//   --data '{}'
