// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Firebase Admin SDK for getting notification token
import { initializeApp } from 'https://esm.sh/firebase-admin/app'
import { getMessaging } from 'https://esm.sh/firebase-admin/messaging'

// Initialize Firebase Admin with service account
try {
  const serviceAccount = Deno.env.get("FIREBASE_SERVICE_ACCOUNT");
  if (!serviceAccount) {
    console.error("FIREBASE_SERVICE_ACCOUNT environment variable is not set");
    throw new Error("FIREBASE_SERVICE_ACCOUNT is required");
  }

  const serviceAccountJson = JSON.parse(serviceAccount);
  
  initializeApp({
    credential: {
      client_email: serviceAccountJson.client_email,
      private_key: serviceAccountJson.private_key,
      project_id: serviceAccountJson.project_id
    }
  });
  
  console.log("Firebase Admin SDK initialized successfully");
} catch (error) {
  console.error("Firebase initialization error:", error);
  throw error;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        'Access-Control-Max-Age': '86400'
      }
    })
  }

  try {
    // Get the request body
    const { userId, platform } = await req.json()

    // Validate request
    if (!userId || !platform) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters' }),
        { headers: { 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Get Firebase token
    const messaging = getMessaging()
    
    // Generate token
    const token = await messaging.getToken()
    
    if (!token) {
      throw new Error('Failed to generate notification token')
    }

    // Store token in Supabase
    const { error: tokenError } = await supabaseClient
      .from('device_tokens')
      .upsert({
        user_id: userId,
        token: token,
        platform: platform,
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    if (tokenError) {
      console.error('Error storing token in Supabase:', tokenError)
      throw tokenError
    }

    return token

    return new Response(
      JSON.stringify({ token }),
      { headers: { 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Error getting notification token:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers: { 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})

// To invoke:
// curl -i --location --request POST 'http://localhost:54321/functions/v1/get-notification-token' \
//   --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
//   --header 'Content-Type: application/json' \
//   --data-raw '{"userId": "user-id", "platform": "web"}'
