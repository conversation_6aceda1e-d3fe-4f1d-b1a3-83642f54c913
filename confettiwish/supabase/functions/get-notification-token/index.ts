// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Generate VAPID keys for web push notifications
// This function generates a simple token for web push subscriptions
function generateWebPushToken(): string {
  // Generate a simple unique token for web push subscriptions
  // In a real implementation, this would be handled by the browser's push manager
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2)
  return `web_push_${timestamp}_${random}`
}

console.log("Web Push token generator initialized successfully");

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        'Access-Control-Max-Age': '86400'
      }
    })
  }

  try {
    // Create a Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get the request body
    const { userId, platform } = await req.json()

    // Validate request
    if (!userId || !platform) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters' }),
        { headers: { 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Generate a simple token for web push
    // Note: In a real implementation, the browser's push manager handles token generation
    // This is a simplified version for demonstration purposes
    let token: string

    if (platform === 'web') {
      // For web, we generate a simple identifier
      // The actual push subscription will be created by the browser
      token = generateWebPushToken()
    } else if (platform === 'android') {
      // For Android, Capacitor Push Notifications plugin handles token generation
      // This would typically be provided by the client
      token = `android_${Date.now()}_${Math.random().toString(36).substring(2)}`
    } else {
      throw new Error(`Unsupported platform: ${platform}`)
    }

    if (!token) {
      throw new Error('Failed to generate notification token')
    }

    // Store token in Supabase
    const { error: tokenError } = await supabaseClient
      .from('device_tokens')
      .upsert({
        user_id: userId,
        token: token,
        platform: platform,
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    if (tokenError) {
      console.error('Error storing token in Supabase:', tokenError)
      throw tokenError
    }

    return new Response(
      JSON.stringify({ token }),
      { headers: { 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error('Error getting notification token:', error)
    return new Response(
      JSON.stringify({ error: errorMessage }),
      { headers: { 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})

// To invoke:
// curl -i --location --request POST 'http://localhost:54321/functions/v1/get-notification-token' \
//   --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
//   --header 'Content-Type: application/json' \
//   --data-raw '{"userId": "user-id", "platform": "web"}'
