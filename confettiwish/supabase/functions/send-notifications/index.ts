// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Web Push library for sending push notifications
import { sendNotification, setVapidDetails } from 'https://esm.sh/web-push@3.6.6'

// Initialize VAPID details for web push
try {
  const vapidPublicKey = Deno.env.get("VAPID_PUBLIC_KEY");
  const vapidPrivateKey = Deno.env.get("VAPID_PRIVATE_KEY");
  const vapidSubject = Deno.env.get("VAPID_SUBJECT") || "mailto:<EMAIL>";

  if (!vapidPublicKey || !vapidPrivateKey) {
    console.error("VAPID keys are not set");
    throw new Error("VAPID_PUBLIC_KEY and VAPID_PRIVATE_KEY are required");
  }

  setVapidDetails(vapidSubject, vapidPublicKey, vapidPrivateKey);
  console.log("VAPID details set successfully");
} catch (error) {
  console.error("VAPID initialization error:", error);
  throw error;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS, GET',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        'Access-Control-Max-Age': '86400'
      }
    })
  }

  console.log('Starting Edge Function');
  console.log('Raw request:', {
    method: req.method,
    headers: Object.fromEntries(req.headers.entries()),
    url: req.url
  });

  try {
    // Handle test endpoint
    if (req.method === 'GET' && req.url.endsWith('/test-webpush')) {
      console.log('Testing Web Push connection...');

      // Test Web Push initialization
      try {
        console.log('Web Push VAPID details set successfully');

        // Test sending a notification to a test device
        const testPayload = JSON.stringify({
          title: 'Test Notification',
          body: 'Web Push connection test successful',
          data: {
            test: 'true',
            url: '/',
            timestamp: new Date().toISOString()
          }
        });

        try {
          // Create a test subscription object (this will fail gracefully)
          const testSubscription = {
            endpoint: 'https://web-push-test.example.com/test-endpoint',
            keys: {
              p256dh: 'test-p256dh-key',
              auth: 'test-auth-key'
            }
          };

          await sendNotification(testSubscription, testPayload);

          return new Response(
            JSON.stringify({
              message: 'Web Push connection test successful'
            }),
            { headers: { 'Content-Type': 'application/json' }, status: 200 }
          );
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          console.error('Web Push send test error:', error);
          return new Response(
            JSON.stringify({
              error: 'Web Push test completed (expected to fail with test data)',
              details: errorMessage
            }),
            { headers: { 'Content-Type': 'application/json' }, status: 200 }
          );
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        console.error('Web Push initialization error:', error);
        return new Response(
          JSON.stringify({
            error: 'Web Push initialization failed',
            details: errorMessage
          }),
          { headers: { 'Content-Type': 'application/json' }, status: 500 }
        );
      }
    }

    // Skip authentication for test endpoint
    if (req.url.endsWith('/test-webpush')) {
      return new Response(
        JSON.stringify({
          message: 'Test endpoint requires no authentication'
        }),
        { headers: { 'Content-Type': 'application/json' }, status: 200 }
      );
    }

    // Verify authorization header exists
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      console.error('No Authorization header provided');
      return new Response(
        JSON.stringify({ error: 'No Authorization header provided' }),
        { headers: { 'Content-Type': 'application/json' }, status: 401 }
      );
    }

    console.log('Authorization header:', authHeader);

    // Create a Supabase client with the Auth context of the logged in user
    const supabaseClient = createClient(
      // Supabase API URL - env var exported by default.
      Deno.env.get('SUPABASE_URL') ?? '',
      // Supabase API ANON KEY - env var exported by default.
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      // Create client with Auth context of the user that called the function.
      {
        global: {
          headers: { Authorization: authHeader },
        },
      }
    )

    // Get the request body
    const { type, userId, data } = await req.json()
    console.log('Parsed request:', { type, userId, data });

    // Validate request
    if (!type || !userId) {
      console.error('Invalid request parameters:', { type, userId });
      return new Response(
        JSON.stringify({ error: 'Missing required parameters' }),
        { headers: { 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Get user's notification preferences
    let { data: preferences, error: prefError } = await supabaseClient
      .from('notification_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()

    console.log('Preferences query result:', { preferences, error: prefError });

    if (prefError) {
      console.error('Error fetching notification preferences:', prefError);
      // Default to enabled if preferences don't exist
      if (prefError.code === 'PGRST116') { // Not found error
        console.log('Creating default preferences');
        const { data: newPrefs, error: insertError } = await supabaseClient
          .from('notification_preferences')
          .insert({
            user_id: userId,
            enabled: true,
            birthday_reminders: true,
            wish_updates: true,
            friend_activity: true
          })
          .select()
          .single()

        if (insertError) {
          console.error('Error creating notification preferences:', insertError);
          return new Response(
            JSON.stringify({ error: 'Failed to create notification preferences' }),
            { headers: { 'Content-Type': 'application/json' }, status: 500 }
          )
        }

        preferences = newPrefs;
        console.log('Created default preferences:', preferences);
      } else {
        console.error('Unexpected error fetching preferences:', prefError);
        return new Response(
          JSON.stringify({ error: 'Failed to fetch notification preferences' }),
          { headers: { 'Content-Type': 'application/json' }, status: 500 }
        )
      }
    }

    console.log('Final preferences:', preferences);

    // Check if notifications are enabled for this type
    if (!preferences.enabled) {
      console.log('Notifications are disabled for user');
      return new Response(
        JSON.stringify({ message: 'Notifications are disabled' }),
        { headers: { 'Content-Type': 'application/json' }, status: 200 }
      )
    }

    // Get device tokens for this user
    const { data: deviceTokens, error: deviceError } = await supabaseClient
      .from('device_tokens')
      .select('*')
      .eq('user_id', userId)

    console.log('Device tokens query result:', { deviceTokens, error: deviceError });

    if (deviceError) {
      console.error('Error fetching device tokens:', deviceError);
      return new Response(
        JSON.stringify({ error: 'Failed to fetch device tokens' }),
        { headers: { 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    console.log('Device tokens:', deviceTokens);

    // Create notification record
    const { data: notification, error: notificationError } = await supabaseClient
      .from('notifications')
      .insert({
        user_id: userId,
        type: type,
        title: data.title,
        body: data.body,
        data: data.data,
        read: false
      })
      .select()
      .single()

    console.log('Notification creation result:', { notification, error: notificationError });

    if (notificationError) {
      console.error('Error creating notification:', notificationError);
      return new Response(
        JSON.stringify({ error: 'Failed to create notification' }),
        { headers: { 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    console.log('Notification created:', notification);

    // Send notifications to all devices
    const sendResults = []
    for (const device of deviceTokens || []) {
      try {
        console.log('Processing device:', device)

        if (device.platform === 'android') {
          // For Android, we'll use Capacitor Push Notifications
          // This requires the mobile app to handle the notification
          console.log('Android notifications require Capacitor Push Notifications plugin')
          sendResults.push({
            device: device.token,
            status: 'skipped',
            message: 'Android notifications handled by Capacitor'
          })
        } else if (device.platform === 'web') {
          // For web, use Web Push API
          try {
            const subscription = JSON.parse(device.token)

            const payload = JSON.stringify({
              title: data.title,
              body: data.body,
              data: {
                ...data.data,
                notificationId: notification.id,
                type: type,
                url: data.data?.url || '/'
              }
            })

            console.log('Sending web push notification to:', subscription.endpoint)
            await sendNotification(subscription, payload)
            sendResults.push({
              device: device.token,
              status: 'sent',
              platform: 'web'
            })
          } catch (webPushError) {
            console.error('Web push error:', webPushError)
            sendResults.push({
              device: device.token,
              status: 'error',
              error: webPushError instanceof Error ? webPushError.message : 'Unknown web push error'
            })
          }
        } else {
          console.error(`Unknown platform: ${device.platform}`)
          sendResults.push({ device: device.token, status: 'error', error: 'Unknown platform' })
          continue
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        console.error(`Error sending to device ${device.token}:`, {
          error: errorMessage,
          stack: error instanceof Error ? error.stack : undefined
        })
        sendResults.push({ device: device.token, status: 'error', error: errorMessage })

        // For web push, invalid subscriptions typically result in 410 Gone responses
        // We can remove invalid tokens to keep the database clean
        if (errorMessage.includes('410') || errorMessage.includes('invalid') || errorMessage.includes('expired')) {
          console.log(`Removing invalid device token: ${device.token}`)
          await supabaseClient
            .from('device_tokens')
            .delete()
            .eq('token', device.token)
        }
      }
    }

    return new Response(
      JSON.stringify({
        message: 'Notification processed',
        notification,
        sendResults
      }),
      { headers: { 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    const errorStack = error instanceof Error ? error.stack : undefined
    console.error('Unexpected error:', {
      error: errorMessage,
      stack: errorStack
    });
    return new Response(
      JSON.stringify({
        error: errorMessage,
        stack: errorStack
      }),
      { headers: { 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})

// To invoke:
// curl -i --location --request POST 'http://localhost:54321/functions/v1/send-notifications' \
//   --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
//   --header 'Content-Type: application/json' \
//   --data '{"type":"birthday_reminder","userId":"user123","data":{"title":"Birthday Reminder","body":"John Doe has a birthday tomorrow!","data":{"url":"/friends/123"}}}'
