// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Firebase Admin SDK for sending push notifications
import { initializeApp } from 'https://esm.sh/firebase-admin/app'
import { getMessaging } from 'https://esm.sh/firebase-admin/messaging'

// Initialize Firebase Admin with service account
try {
  const serviceAccount = Deno.env.get("FIREBASE_SERVICE_ACCOUNT");
  if (!serviceAccount) {
    console.error("FIREBASE_SERVICE_ACCOUNT environment variable is not set");
    throw new Error("FIREBASE_SERVICE_ACCOUNT is required");
  }

  const serviceAccountJson = JSON.parse(serviceAccount);

  initializeApp({
    credential: {
      client_email: serviceAccountJson.client_email,
      private_key: serviceAccountJson.private_key,
      project_id: serviceAccountJson.project_id
    }
  });

  console.log("Firebase Admin SDK initialized successfully");
} catch (error) {
  console.error("Firebase initialization error:", error);
  throw error;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS, GET',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        'Access-Control-Max-Age': '86400'
      }
    })
  }

  console.log('Starting Edge Function');
  console.log('Raw request:', {
    method: req.method,
    headers: Object.fromEntries(req.headers.entries()),
    url: req.url
  });

  try {
    // Handle test endpoint
    if (req.method === 'GET' && req.url.endsWith('/test-firebase')) {
      console.log('Testing Firebase connection...');

      // Test Firebase Admin SDK initialization
      try {
        const messaging = getMessaging();
        console.log('Firebase Admin SDK initialized successfully');

        // Test sending a notification to a test device
        const testMessage = {
          notification: {
            title: 'Test Notification',
            body: 'Firebase connection test successful'
          },
          data: {
            test: 'true'
          }
        };

        try {
          // We'll use a test device token - this will fail if the token is invalid
          const testToken = 'test-device-token';
          const response = await messaging.send({
            ...testMessage,
            token: testToken
          });

          return new Response(
            JSON.stringify({
              message: 'Firebase connection test successful',
              response
            }),
            { headers: { 'Content-Type': 'application/json' }, status: 200 }
          );
        } catch (error) {
          console.error('Firebase send test error:', error);
          return new Response(
            JSON.stringify({
              error: 'Firebase send test failed',
              details: error.message
            }),
            { headers: { 'Content-Type': 'application/json' }, status: 500 }
          );
        }
      } catch (error) {
        console.error('Firebase initialization error:', error);
        return new Response(
          JSON.stringify({
            error: 'Firebase initialization failed',
            details: error.message
          }),
          { headers: { 'Content-Type': 'application/json' }, status: 500 }
        );
      }
    }

    // Skip authentication for test endpoint
    if (req.url.endsWith('/test-firebase')) {
      return new Response(
        JSON.stringify({
          message: 'Test endpoint requires no authentication'
        }),
        { headers: { 'Content-Type': 'application/json' }, status: 200 }
      );
    }

    // Verify authorization header exists
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      console.error('No Authorization header provided');
      return new Response(
        JSON.stringify({ error: 'No Authorization header provided' }),
        { headers: { 'Content-Type': 'application/json' }, status: 401 }
      );
    }

    console.log('Authorization header:', authHeader);

    // Create a Supabase client with the Auth context of the logged in user
    const supabaseClient = createClient(
      // Supabase API URL - env var exported by default.
      Deno.env.get('SUPABASE_URL') ?? '',
      // Supabase API ANON KEY - env var exported by default.
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      // Create client with Auth context of the user that called the function.
      {
        global: {
          headers: { Authorization: authHeader },
        },
      }
    )

    // Get the request body
    const { type, userId, data } = await req.json()
    console.log('Parsed request:', { type, userId, data });

    // Validate request
    if (!type || !userId) {
      console.error('Invalid request parameters:', { type, userId });
      return new Response(
        JSON.stringify({ error: 'Missing required parameters' }),
        { headers: { 'Content-Type': 'application/json' }, status: 400 }
      )
    }

    // Get user's notification preferences
    const { data: preferences, error: prefError } = await supabaseClient
      .from('notification_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()

    console.log('Preferences query result:', { preferences, error: prefError });

    if (prefError) {
      console.error('Error fetching notification preferences:', prefError);
      // Default to enabled if preferences don't exist
      if (prefError.code === 'PGRST116') { // Not found error
        console.log('Creating default preferences');
        const { data: newPrefs, error: insertError } = await supabaseClient
          .from('notification_preferences')
          .insert({
            user_id: userId,
            enabled: true,
            birthday_reminders: true,
            wish_updates: true,
            friend_activity: true
          })
          .select()
          .single()

        if (insertError) {
          console.error('Error creating notification preferences:', insertError);
          return new Response(
            JSON.stringify({ error: 'Failed to create notification preferences' }),
            { headers: { 'Content-Type': 'application/json' }, status: 500 }
          )
        }

        preferences = newPrefs;
        console.log('Created default preferences:', preferences);
      } else {
        console.error('Unexpected error fetching preferences:', prefError);
        return new Response(
          JSON.stringify({ error: 'Failed to fetch notification preferences' }),
          { headers: { 'Content-Type': 'application/json' }, status: 500 }
        )
      }
    }

    console.log('Final preferences:', preferences);

    // Check if notifications are enabled for this type
    if (!preferences.enabled) {
      console.log('Notifications are disabled for user');
      return new Response(
        JSON.stringify({ message: 'Notifications are disabled' }),
        { headers: { 'Content-Type': 'application/json' }, status: 200 }
      )
    }

    // Get device tokens for this user
    const { data: deviceTokens, error: deviceError } = await supabaseClient
      .from('device_tokens')
      .select('*')
      .eq('user_id', userId)

    console.log('Device tokens query result:', { deviceTokens, error: deviceError });

    if (deviceError) {
      console.error('Error fetching device tokens:', deviceError);
      return new Response(
        JSON.stringify({ error: 'Failed to fetch device tokens' }),
        { headers: { 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    console.log('Device tokens:', deviceTokens);

    // Create notification record
    const { data: notification, error: notificationError } = await supabaseClient
      .from('notifications')
      .insert({
        user_id: userId,
        type: type,
        title: data.title,
        body: data.body,
        data: data.data,
        read: false
      })
      .select()
      .single()

    console.log('Notification creation result:', { notification, error: notificationError });

    if (notificationError) {
      console.error('Error creating notification:', notificationError);
      return new Response(
        JSON.stringify({ error: 'Failed to create notification' }),
        { headers: { 'Content-Type': 'application/json' }, status: 500 }
      )
    }

    console.log('Notification created:', notification);

    // Send notifications to all devices
    const sendResults = []
    for (const device of deviceTokens || []) {
      try {
        console.log('Processing device:', device)

        let message
        if (device.platform === 'android') {
          // For Android, use the token directly
          message = {
            token: device.token,
            notification: {
              title: data.title,
              body: data.body,
            },
            data: {
              ...data.data,
              notificationId: notification.id,
              type: type
            },
            android: {
              priority: 'high',
            }
          }
        } else if (device.platform === 'web') {
          // For web, we need to use the endpoint and keys from the token
          const tokenJson = JSON.parse(device.token)
          message = {
            notification: {
              title: data.title,
              body: data.body,
            },
            data: {
              ...data.data,
              notificationId: notification.id,
              type: type
            },
            webpush: {
              headers: {
                'TTL': '604800', // 7 days in seconds
              },
              fcmOptions: {
                link: data.data.url || '/',
              }
            }
          }

          console.log('Sending web notification to:', tokenJson.endpoint)
          // Send to Firebase Cloud Messaging using the web token
          const response = await getMessaging().sendToDevice(
            tokenJson.endpoint,
            message,
            {
              vapidKey: Deno.env.get('FIREBASE_PUBLIC_KEY')
            }
          )
          sendResults.push({ device: device.token, status: 'sent', messageId: response })
        } else {
          console.error(`Unknown platform: ${device.platform}`)
          sendResults.push({ device: device.token, status: 'error', error: 'Unknown platform' })
          continue
        }

        // Send the message using Firebase Cloud Messaging
        if (device.platform === 'android') {
          console.log('Sending Android notification')
          const response = await getMessaging().send(message)
          sendResults.push({ device: device.token, status: 'sent', messageId: response })
        }
      } catch (error) {
        console.error(`Error sending to device ${device.token}:`, {
          error: error.message,
          stack: error.stack,
          code: error.code
        })
        sendResults.push({ device: device.token, status: 'error', error: error.message })

        if (error.code === 'messaging/invalid-registration-token' ||
            error.code === 'messaging/registration-token-not-registered') {
          await supabaseClient
            .from('device_tokens')
            .delete()
            .eq('token', device.token)
        }
      }
    }

    return new Response(
      JSON.stringify({
        message: 'Notification processed',
        notification,
        sendResults
      }),
      { headers: { 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Unexpected error:', {
      error: error.message,
      stack: error.stack
    });
    return new Response(
      JSON.stringify({
        error: error.message,
        stack: error.stack
      }),
      { headers: { 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})

// To invoke:
// curl -i --location --request POST 'http://localhost:54321/functions/v1/send-notifications' \
//   --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
//   --header 'Content-Type: application/json' \
//   --data '{"type":"birthday_reminder","userId":"user123","data":{"title":"Birthday Reminder","body":"John Doe has a birthday tomorrow!","data":{"url":"/friends/123"}}}'
