# ConfetiiWish Web Application Design Document (v1.0)

## Application Overview
ConfetiiWish is a birthday and gift management application that helps users track friends' birthdays and manage gift wishlists. The application follows a mobile-first design but should be responsive for web interfaces.

## Data Models

### User (Supabase Auth User + Profiles)
```typescript
interface User {
    id: string;             // Supabase Auth UUID
    email: string;          // Supabase Auth email
    phoneNumber?: string;   
    address?: string;
    personalInfo: {
        name: string;
        birthday: Date;
        profileImage?: string;
    };
    onboardingComplete: boolean;
    emailVerified: boolean;  // Managed by Supabase Auth
}
```

### Friend
```typescript
interface Friend {
    id: string;
    userId: string;
    name: string;
    email?: string;         // Added email as optional contact method
    phoneNumber?: string;   // Made optional to support either contact method
    address?: string;
    birthday: Date;
    profileImage?: string;
    notes?: string;
}
```

### Wish
```typescript
interface Wish {
    id: string;
    friendId: string;
    title: string;
    description?: string;
    price?: number;
    link?: string;
    isPurchased: boolean;
    whereToFind: string;
    priority: 'Low' | 'Medium' | 'High';
    addedByUser: boolean;
}
```

## Page Flows and Features

### 1. Onboarding Flow
Onboarding will be a multi-step process:
1. Email Authentication
2. Personal Information
3. Option to import friends from contacts

#### Email Authentication Screen (`/auth/login`)
- Email input field
  - Validation for proper email format
  - Supabase Auth OTP verification
- Submit button
  - Triggers Supabase OTP email
  - Right arrow icon for next step
- OTP verification
  - 6-digit numeric code input
  - Input validation (numbers only)
  - Auto-focus on input field
- Success state
  - Confirmation message to check email
  - Option to resend OTP
  - Timer for resend functionality (prevent abuse)
  - Clear indication of verification status
- Error handling
  - Invalid email format
  - Invalid OTP format
  - Incorrect OTP
  - Email sending failures
  - OTP expiration
  - Use notifier for error messages

#### Personal Information Screen (`/auth/personal-info`)
- Name input field (required)
- Birthday date picker (required)
- Profile image upload (optional)
  - Use common image upload component
  - Preview and crop functionality
  - Default profile picture will be the user's name initials
- Submit button
- Form validation

#### Import Friends Screen (`/auth/import-contacts`)
- Options of where to source the contacts from
  - Contacts app
    - Use Capacitor Contacts plugin
    - Request permissions
    - Process and validate contacts
      - Name is required
      - Email or phone number is required
      - Birthday is optional
      - Phone number format validation
      - Email format validation
      - Birthday format validation
  - CSV file upload
  - CSV format guidelines
  - CSV file upload component
  - CSV processing and validation
    - Birthaday is optional for CSV import
    - Name is required
    - Email or phone number is required
    - Birthday format validation
    - Phone number format validation
    - Email format validation
  - Error handling for invalid CSV format
  - Preview of imported contacts
  - Select contacts to import
- Import button
- Progress indicator
- Error handling
  - Permission denied
  - No contacts found
  - Import failure
  - Use notifier for error messages

### 2. Main Application Flow

#### Navigation
- Bottom navigation bar (mobile) / Side navigation (desktop) with:
  - Birthday timeline (default)
  - Friends list
  - Profile section
    - Personal information
    - My wishes
    - Settings
    - Logout
    - Delete account
    - Located on the top right of all pages

#### Friends Management Screen (`/friends`)
Features:
- Search bar for filtering friends
  - Filters by name or phone number
  - Clear button to reset search
  - Live update of results
  - Search must include all friends regardless of pagination
- Filter by friends with birthdays
- Add new friend button
- Friend card list with:
  - Sorting is alphabetical by name.
  - Profile picture
  - Name
  - Days until birthday
    - If birthday is today, display "Today"
    - If in next 7 days, highlight in bold, red
    - If in next 30 days, highlight in bold, orange
    - If more than 30 days, green
    - If no birthday is set, display "No DOB" in orange
  - Quick actions (edit, delete)
    - Clicking on a friend opens their wish list
    - Edit icon is a pencil icon 
    - Delete icon is a trash can icon
    - Edit opens a new page for updating friend's information
      - Includes wish list management
    - Delete confirmation dialog
      - Displays friend's name and confirmation message
      - Delete action is irreversible
      - Removes all associated wishes
- Import from contacts button
  - Redirects to import contacts page; same as when onboarding

Add/Edit Friend Page:
- Profile image upload
  - Use common image upload component
  - Preview and crop functionality
  - Default profile picture will be the user's name initials
- Name (required)
- Phone number (optional)
- Birthday (required)
- Notes (optional)
- Wish list
- Optional address for online deliveries
- Save/Cancel buttons

#### Birthday Timeline Screen (`/birthdays`)
Features:
- Chronological list of upcoming birthdays
  - Cards are sorted by days until birthday
  - Cards are limited to 20 per page
  - Cards are paginated - automatically loads more as you scroll
    - Load more button for additional birthdays as backup
- Visual indicators for:
  - Birthdays within 7 days (highlight)
  - Birthdays within 30 days (highlight)
  - Today's birthdays (special highlight)
- Birthday cards showing:
  - Friend's name and photo
  - Age they will be
  - Days until birthday
  - Quick link to wish list
    - Displays all wishes for the friend
  - Share birthday reminder button
    - Friend being shared must be signed up in the app
      - If not send an email to the friend to sign up and includes a link to the app
        - Include a request to allow sharing of their birthday and information with the user
      - If they are signed up already, send a request to the app to verify that thier info can be shared with the target.
      - If the Friend being shared grants permission, the system can send the invite to the original target.
    - Sends an invite to the friend's app
      - Select from contacts if available
      - Enter email address if not already in contacts
      - Validate email format
      - If the email where the invite is being sent exists as a profile in the app, send the invite to that users app directly to add a friend.
      - If the email where the invite is being sent does not exist as a profile in the app, send the invite to signup to the app first. Once they signup they can add the friend.
    - Includes a pre-filled message with the friend's name and birthday
    - Option to customize message
    - Sends the invite via supabase edge function.
      - Edge function checks if the target is an app user (has a profile).
      - If the target is an app user, send the invite to the app.
      - If the target is not an app user, send the invite to create an account via the Brevo email system.
      - Brevo sends the email and tracks the open and click rates.
      - Brevo sends a webhook to the supabase edge function with the email status.
      - The edge function updates the database with the email status.
    - The email includes a tracking pixel to track opens and clicks.
    - The email includes a link to the app to add the friend.
    - The email includes a link to the app to signup if they don't have an account.
    - The email includes a link to the app to verify their information if they already have an account.
    - The email includes a link to the app to manage their notifications.

#### Wish Management Screen (`/friends/{friendId}/wishes`)
Accessable from friend card or birthday timeline
Features:
- Friend's profile summary
- Add new wish button
- Request wish list from friend CTA
- Wish list with filtering options:
  - All wishes
  - Purchased/Not purchased
  - Priority level
  - Added by user/friend

Add/Edit Wish Page:
- Title (required)
- Description (optional)
- Price (optional)
  - Currency selection (use localization store)
  - Price validation (number only)
  - Price display based on user's locale and currency
- Priority selection (required)
- Where to find (required)
- Online store link (optional)
- Purchase status toggle
- Save/Cancel buttons

#### Profile Screen (`/profile`)
Features:
- User profile image
  - Use common image upload component
  - Preview and crop functionality
  - Default profile picture will be the user's name initials
- Personal information display
  - Name
  - Birthday
  - Phone number
  - Address (if provided)
- Edit profile button
- My Wishes management options
- Application settings:
  - Theme preference
  - Notification settings
  - Locale and currency settings
    - When selecting a Locale, the currency is automatically updated to the default for that locale unless a custom currency has been selected
    - When selecting a Currency, the locale is not changed
    - The currency symbol is automatically updated based on the selected currency
  - Delete account
    - Confirmation dialog
    - Irreversible action
    - Removes all associated data
  - Logout
    - Takes user to the login page

## Use Cases

### Friend Management
1. Add new friend
2. Edit friend's information
3. Delete friend
4. Search for specific friend
5. View friend's wish list
6. Import friends from contacts
7. Request friend's wish list
9. Share friend's wish list

### Birthday Tracking
1. View upcoming birthdays
2. Receive birthday notifications
3. Filter birthdays by time period
4. Share birthday reminder

### Wish Management
1. Add wish to friend's list
2. Edit wish details
3. Mark wish as purchased
4. Share wish with other friends
5. Add notes to wishes
6. Track wish prices
7. Open wish link in online store

### User Profile
1. Update personal information
2. Change profile picture
3. Manage notification preferences
4. Export data
5. Change language/theme

## Technical Considerations

### Security
- Phone number verification
- Data encryption
- Authentication and authorization
- Secure API endpoints
- Input validation

### Performance
- Image optimization
- Lazy loading
- Caching strategies
- Offline functionality

### Accessibility
- ARIA labels
- Keyboard navigation
- Screen reader compatibility
- Color contrast compliance

### Responsive Design
- Mobile-first approach
- Desktop layout optimization
- Touch-friendly interfaces
- Flexible grid system

### Email System
- Brevo Integration
  - Transactional emails for notifications
  - Birthday reminder templates
  - Wish list sharing templates
  - Email tracking and analytics
  - Contact management for users
  - Email scheduling for birthday reminders

### Notification Types
- Birthday Reminders
  - Scheduled X days before (user configurable)
  - Custom templates with friend's details
  - Personalized content
- Wish List Updates
  - Share wish lists with other users
  - Purchase notifications
  - New wish added notifications
- System Notifications
  - Account verification
  - Profile updates
  - Security alerts

## Styling and UI/UX Guides
- Consistent color scheme
  - Blue, pink, and green hues
  - Light and dark themes
- Clear and intuitive navigation
- Attractive and engaging UI elements
- Mobile-friendly design
- Smooth animations and transitions
- Consistent typography

## Error Handling
- Network connectivity issues
- Invalid input validation
- API failure responses
- Image upload errors
- Database transaction failures

## Future Enhancements
1. Social sharing features
2. Gift price tracking and budgeting
3. Gift suggestion algorithm
4. Multiple wishlists per friend
5. Group gift coordination
6. Calendar integration
7. E-commerce platform integration
8. Gift purchase history

## Logo colour pallet
| Element          | Color Name       | Hex Code   |
|------------------|------------------|------------|
| Confetti Pink    | Pastel Coral     | `#F7A8A1`  |
| Confetti Teal    | Soft Aqua        | `#A3E4DB`  |
| Lavender Accent  | Pastel Lavender  | `#D6C8F5`  |
| Background       | Off-white cream  | `#FFF9F2`  |
| Wordmark Text    | Cool Grey        | `#5E5E5E`  |

